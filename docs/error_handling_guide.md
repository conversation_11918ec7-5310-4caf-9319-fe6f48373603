# Error Handling and Logging Guide

## Overview

This document describes the comprehensive error handling and logging system implemented in the mensetsu-kun WebSocket/WebRTC application. The system provides centralized error management, structured logging, and user-friendly error reporting.

## Architecture

### Core Components

1. **ErrorHandler** (`app/core/error_handling.py`)
   - Centralized error creation and management
   - Error classification and severity assessment
   - User-friendly message mapping
   - Error statistics and monitoring

2. **WebSocketErrorHandler** (`app/core/error_handling.py`)
   - Specialized WebSocket error handling
   - Connection and message error management
   - WebSocket-specific error recovery

3. **LoggingConfig** (`app/core/logging_config.py`)
   - Structured logging configuration
   - Multiple log formats (JSON, detailed, simple)
   - Performance logging decorators
   - Specialized loggers for different components

## Error Classification

### Error Categories

- **WEBSOCKET**: WebSocket connection and communication errors
- **WEBRTC**: WebRTC peer connection and media errors
- **VIDEO_PROCESSING**: Video recording and frame processing errors
- **AUDIO_PROCESSING**: Audio generation and TTS errors
- **NETWORK**: Network connectivity and timeout errors
- **VALIDATION**: Input validation and format errors
- **AUTHENTICATION**: Authentication and authorization errors
- **RESOURCE**: System resource and capacity errors
- **CONFIGURATION**: Configuration and setup errors
- **EXTERNAL_SERVICE**: Third-party service integration errors

### Error Severity Levels

- **LOW**: Minor issues that don't affect core functionality
- **MEDIUM**: Issues that may impact user experience
- **HIGH**: Significant problems affecting core features
- **CRITICAL**: System-level failures requiring immediate attention

### Error Codes

Standardized error codes follow a pattern: `{CATEGORY}_{NUMBER}`

Examples:
- `WS_001`: WebSocket connection failed
- `RTC_001`: WebRTC offer failed
- `VID_001`: Video recording failed
- `AUD_001`: Audio TTS failed
- `NET_001`: Network timeout
- `VAL_001`: Validation message format error

## Usage Examples

### Creating and Handling Errors

```python
from app.core.error_handling import error_handler, ErrorCode, ErrorContext, ErrorSeverity

# Create error context
context = ErrorContext(
    session_id="session_123",
    operation="video_processing",
    component="video_manager",
    additional_data={"frame_count": 150}
)

# Create error
error_info = error_handler.create_error(
    code=ErrorCode.VIDEO_RECORDING_FAILED,
    message="Failed to process video frame",
    context=context,
    severity=ErrorSeverity.HIGH,
    recovery_suggestions=[
        "Check video codec support",
        "Verify storage permissions",
        "Restart video recording"
    ]
)

# Send error to WebSocket client
await error_handler.send_error_to_websocket(websocket, error_info)
```

### WebSocket Error Handling

```python
from app.core.error_handling import websocket_error_handler

# Handle connection errors
error_info = await websocket_error_handler.handle_connection_error(
    websocket, session_id, exception, "connection_setup"
)

# Handle message processing errors
error_info = await websocket_error_handler.handle_message_error(
    websocket, session_id, message_data, exception, "message_validation"
)
```

### Performance Logging

```python
from app.core.logging_config import log_performance

@log_performance("video_processing", "video")
async def process_video_frame(session_id: str, frame):
    # Function implementation
    pass
```

### Structured Logging

```python
import logging

logger = logging.getLogger(__name__)

# Log with context
logger.info("Video processing started", extra={
    'session_id': session_id,
    'operation': 'video_start',
    'component': 'video_manager',
    'frame_rate': 30
})
```

## Configuration

### Environment Variables

- `LOG_LEVEL`: Set logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_DIR`: Directory for log files (default: "logs")
- `LOG_FORMAT`: Log format type (simple, detailed, json)

### Logging Setup

```python
from app.core.logging_config import setup_logging

# Setup logging for development
config = setup_logging(
    environment="development",
    log_level="DEBUG",
    log_dir="logs"
)

# Setup logging for production
config = setup_logging(
    environment="production",
    log_level="INFO",
    log_dir="/var/log/mensetsu-kun"
)
```

## Log Files

The system creates specialized log files:

- `application.log`: All application logs
- `error.log`: Error-level logs only
- `websocket.log`: WebSocket-specific logs
- `webrtc.log`: WebRTC-specific logs
- `video.log`: Video processing logs
- `performance.log`: Performance metrics
- `security.log`: Security-related events

## Error Recovery

### Retry Mechanisms

The system includes automatic retry logic for recoverable errors:

```python
# Check if error should be retried
if error_handler.should_retry(error_info):
    delay = error_handler.get_retry_delay(error_info)
    await asyncio.sleep(delay)
    # Retry operation
```

### Recovery Suggestions

Each error includes user-friendly recovery suggestions:

- Network errors: "Check network connection", "Refresh the page"
- Video errors: "Check camera permissions", "Restart video recording"
- Audio errors: "Check microphone settings", "Verify audio codec support"

## Monitoring and Alerting

### Error Statistics

```python
# Get error statistics
stats = error_handler.get_error_stats()
print(f"Total errors: {stats['total_errors']}")
print(f"Recent errors: {stats['recent_errors_count']}")
print(f"Error rate: {stats['error_rate_per_hour']}/hour")
```

### Integration with Azure Application Insights

The system integrates with Azure Application Insights for:
- Error tracking and alerting
- Performance monitoring
- Custom metrics and telemetry
- Distributed tracing

## Best Practices

### Error Handling

1. **Always use error codes**: Use standardized error codes for consistent error identification
2. **Provide context**: Include relevant context information (session_id, operation, component)
3. **Set appropriate severity**: Choose severity levels that reflect the actual impact
4. **Include recovery suggestions**: Provide actionable guidance for users
5. **Log errors appropriately**: Use structured logging with relevant metadata

### Logging

1. **Use structured logging**: Include context fields for better searchability
2. **Choose appropriate log levels**: DEBUG for development, INFO for production
3. **Include performance metrics**: Use performance decorators for critical operations
4. **Avoid sensitive data**: Never log passwords, tokens, or personal information
5. **Use specialized loggers**: Use component-specific loggers for better organization

### Performance

1. **Minimize logging overhead**: Use appropriate log levels to reduce I/O
2. **Batch error reporting**: Group related errors when possible
3. **Implement circuit breakers**: Prevent cascading failures
4. **Monitor error rates**: Set up alerts for unusual error patterns
5. **Regular log rotation**: Configure log rotation to manage disk space

## Troubleshooting

### Common Issues

1. **High error rates**: Check network connectivity and external service status
2. **Memory issues**: Monitor error history size and implement cleanup
3. **Log file growth**: Ensure log rotation is properly configured
4. **Performance degradation**: Review performance logs and optimize bottlenecks

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

### Error Analysis

Use error statistics to identify patterns:

```python
stats = error_handler.get_error_stats()
for category, count in stats['category_breakdown'].items():
    print(f"{category}: {count} errors")
```

## Migration Guide

### From Basic Error Handling

1. Replace simple try-catch blocks with structured error handling
2. Update error messages to use error codes
3. Add context information to error creation
4. Implement retry logic for recoverable errors
5. Update client-side error handling to use new error format

### Example Migration

Before:
```python
try:
    result = await some_operation()
except Exception as e:
    logger.error(f"Operation failed: {e}")
    await websocket.send_text(json.dumps({"type": "error", "message": "Operation failed"}))
```

After:
```python
try:
    result = await some_operation()
except Exception as e:
    context = ErrorContext(
        session_id=session_id,
        operation="some_operation",
        component="component_name"
    )
    error_info = error_handler.create_error(
        code=ErrorCode.OPERATION_FAILED,
        message=f"Operation failed: {str(e)}",
        context=context,
        exception=e
    )
    await error_handler.send_error_to_websocket(websocket, error_info)
```
