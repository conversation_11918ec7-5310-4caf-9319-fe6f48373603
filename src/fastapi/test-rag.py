import asyncio
import json
from app.services.rag.candidate_rag_service import CandidateRAGService

def print_formatted_json(data, title=""):
    if title:
        print(f"\n{'='*60}")
        print(f"📋 {title}")
        print(f"{'='*60}")
    
    if isinstance(data, dict):
        print(json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print(data)
    print()

async def main():
    candidate_service = CandidateRAGService()
    user_id = "ece0c3bb-a26b-45c3-a729-85df9153aed9"
    
    search_results_python = await candidate_service.search_candidate_info(
        user_id=user_id, 
        query="Tôi cần tìm ứng viên có kỹ năng làm việc với java?",
        limit=5,
        relevance_threshold=0.5
    )
    
    print_formatted_json(search_results_python, "KẾT QUẢ TÌM KIẾM JAVA")
    
if __name__ == "__main__":
    asyncio.run(main())