# fastapi/app/models/candidate.py
from datetime import datetime
from sqlalchemy import <PERSON>olean, Column, DateTime, Enum, Integer, JSON, String, ForeignKey
from app.models.base import Base
import enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.orm import Session
from app.models.schemas.candidate import CandidateRegisterRequest
import uuid

class EmploymentStatus(str, enum.Enum):
    employed = "employed"
    unemployed = "unemployed"

class Candidate(Base):
    __tablename__ = "candidates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False, index=True)
    user = relationship("User", back_populates="candidate_profile")

    name_kana = Column(String, nullable=False)
    age = Column(Integer, nullable=False)
    employment_status = Column(String(20), nullable=False)  # "employed", "unemployed"
    company = Column(String, nullable=True)
    position = Column(String, nullable=True)
    address = Column(String, nullable=True)
    has_resume = Column(Boolean, default=False)
    has_career_history = Column(Boolean, default=False)
    profile = Column(JSON, nullable=True)
    main_skill = Column(String, nullable=True)
    other_skill = Column(String, nullable=True)
    experience = Column(String, nullable=True)
    education = Column(String, nullable=True)
    language = Column(String, nullable=True)
    other_info = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    documents = relationship(
        "CandidateDocument", back_populates="candidate", cascade="all, delete-orphan", lazy="select")

    def __repr__(self):
        return f"<Candidate(id={self.id}, user_id={self.user_id})>"
    
    @staticmethod
    async def create_candidate(db: Session, payload: CandidateRegisterRequest, user_id: UUID):
        candidate = Candidate(
            user_id=user_id,
            name_kana=payload.candidate_name_kana,
            age=payload.candidate_age,
            employment_status=payload.candidate_employment_status,
            company=payload.candidate_company,
            position=payload.candidate_position,
            address=payload.candidate_address,
            has_resume=payload.candidate_has_resume,
            has_career_history=payload.candidate_has_career_history,
            profile=payload.candidate_profile,
            main_skill=payload.candidate_main_skill,
            other_skill=payload.candidate_other_skill,
            experience=payload.candidate_experience,
            education=payload.candidate_education,
            language=payload.candidate_language,
            other_info=payload.candidate_other_info,
        )

        db.add(candidate)
        db.commit()
        db.refresh(candidate)
        return candidate
    
    @staticmethod
    async def get_all_candidates(db: Session, skip: int, limit: int):
        candidates = db.query(Candidate).offset(skip).limit(limit).all()
        return candidates
    
    @staticmethod
    async def get_candidate_by_user_id(db: Session, user_id: UUID):
        candidate = db.query(Candidate).filter(Candidate.user_id == user_id).first()
        return candidate
    
class CandidateDocument(Base):
    __tablename__ = "candidate_documents"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    document_type = Column(String(50), nullable=False)  # "resume", "cv", "portfolio", "certificate"
    file_size = Column(Integer)
    mime_type = Column(String(100))
    upload_date = Column(DateTime, default=datetime.utcnow)
    processed = Column(Boolean, default=False)
    text_chunks_count = Column(Integer, default=0)
    
    candidate = relationship("Candidate", back_populates="documents")