# app/models/schemas/company.py
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional, List

class CompanyCreate(BaseModel):
    name: str
    industry: Optional[str] = None
    description: Optional[str] = None
    core_values: Optional[List[str]] = None
    vision_mission: Optional[str] = None

class CompanyUpdate(BaseModel):
    name: str
    industry: Optional[str] = None
    description: Optional[str] = None
    core_values: Optional[List[str]] = None
    vision_mission: Optional[str] = None

class CompanyResponse(BaseModel):
    id: UUID
    name: str
    industry: Optional[str] = None
    description: Optional[str] = None
    core_values: Optional[List[str]] = None
    vision_mission: Optional[str] = None
    created_at: datetime
    ai_summary: Optional[str] = None
    key_insights: Optional[List[str]] = None