# app/models/schemas/interview.py
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional, List

class InterviewCreate(BaseModel):
    candidate_name: str

class InterviewResponse(BaseModel):
    id: str
    candidate_name: str
    candidate_profile: dict
    status: str
    interview_url: str
    created_at: datetime
    updated_at: datetime
    transcript: list
    feedback: list
    overall_score: int