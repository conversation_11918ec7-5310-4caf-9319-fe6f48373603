from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.models.base import get_db
from app.models.interview import InterviewSession
from app.models.company import Company
from pydantic import BaseModel
from typing import Dict
from uuid import UUID

router = APIRouter()

class InterviewStart(BaseModel):
    interview_url: str
    interview_id: str

class InterviewResponse(BaseModel):
    id: str
    company_id: str
    candidate_name: str
    status: str
    created_at: str

@router.post("/join", response_model=InterviewResponse)
async def join_interview(
    interview_data: InterviewStart, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_url(db, interview_data.interview_url)    
    
    if not interview:
        raise HTTPException(status_code=404, detail="Company not found")
    
    if interview.status != "created":
        raise HTTPException(status_code=400, detail="Interview already started")
    
    interview.status = "joined"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )
    
@router.post("/{interview_id}/start", response_model=InterviewResponse)
async def start_interview(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_id(db, UUID(interview_id))
    
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")
    
    if interview.status != "joined":
        raise HTTPException(status_code=400, detail="Interview not joined")

    interview.status = "started"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )

@router.get("/{interview_id}/feedback")
async def get_interview_feedback(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Get interview feedback"""
    
    session = db.query(InterviewSession).filter(
        InterviewSession.id == interview_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Interview session not found")
    
    if not session.feedback:
        raise HTTPException(status_code=400, detail="Interview not completed yet")
    
    return {
        "session_id": str(session.id),
        "feedback": session.feedback,
        "overall_score": session.overall_score,
        "status": session.status
    }
