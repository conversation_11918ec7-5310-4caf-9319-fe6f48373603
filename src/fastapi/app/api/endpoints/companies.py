# backend/app/api/endpoints/companies.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.models.base import get_db
from app.models.company import Company
from app.services.rag.company_rag_service import CompanyRAGService
from app.services.document.document_service import DocumentService
from app.models.schemas.company import CompanyCreate, CompanyResponse
from app.models.schemas.company_keypoint import CompanyKeypointCreate, CompanyKeypointResponse
from uuid import UUID
from fastapi import UploadFile, File
from typing import Dict, Any, List, Optional
import logging
from app.models.schemas.keypoint_template import KeypointTemplateCreate, KeypointTemplateResponse
from datetime import datetime
from app.models.keypoint_template import KeypointTemplate
from app.models.company import CompanyKeypoint

logger = logging.getLogger(__name__)

router = APIRouter()
company_rag_service = CompanyRAGService()
document_service = DocumentService()

@router.post("/", response_model=CompanyResponse)
async def create_company(company: CompanyCreate, db: Session = Depends(get_db)):
    """Create company"""
    db_company = Company.create_company(db, company)
    
    company_data = {
        "name": company.name,
        "description": company.description,
        "core_values": company.core_values,
        "vision_mission": company.vision_mission
    }
    
    company_rag_service.load_company_data(str(db_company.id), company_data)
    
    return CompanyResponse(
        id=db_company.id,
        name=db_company.name,
        industry=db_company.industry,
        description=db_company.description,
        core_values=db_company.core_values,
        vision_mission=db_company.vision_mission,
        created_at=db_company.created_at,
    )

@router.get("/{company_id}", response_model=CompanyResponse)
async def get_company(company_id: str, db: Session = Depends(get_db)):
    """Get company information with AI analysis"""
    company = Company.get_company_by_id(db, UUID(company_id))
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    company_data = {
        "name": company.name,
        "industry": company.industry,
        "description": company.description,
        "core_values": company.core_values,
        "vision_mission": company.vision_mission
    }
    
    ai_summary, key_insights = company_rag_service.analyze_company_with_ai(
        company_data, str(company.id)
    )
    
    return CompanyResponse(
        id=str(company.id),
        name=company.name,
        industry=company.industry,
        description=company.description,
        core_values=company.core_values,
        vision_mission=company.vision_mission,
        created_at=company.created_at,
        ai_summary=ai_summary,
        key_insights=key_insights
    )

@router.post("/{company_id}/documents")
async def upload_company_documents(
    company_id: str, 
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Upload company documents with automatic RAG synchronization"""
    company = Company.get_company_by_id(db, UUID(company_id))
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    file_extension = file.filename.lower().split('.')[-1]
    if file_extension not in ['pdf', 'doc', 'docx']:
        raise HTTPException(
            status_code=400, 
            detail="Only PDF, DOC, DOCX files are supported"
        )
    
    try:
        company_data = {
            "name": company.name,
            "industry": company.industry,
            "description": company.description,
            "core_values": company.core_values,
            "vision_mission": company.vision_mission
        }
        
        document, extracted_text, rag_sync_result = await document_service.save_company_document_with_rag_sync(
            file=file,
            company_id=UUID(company_id),
            company_data=company_data,
            db=db,
            sync_to_rag=True
        )
        
        return {
            "id": str(document.id),
            "file_name": document.file_name,
            "file_size": document.file_size,
            "processed": document.processed,
            "extracted_text_length": len(extracted_text) if extracted_text else 0,
            "rag_synced": rag_sync_result.get("sync_status") == "success" if rag_sync_result else False
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document for company {company_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload document: {str(e)}"
        )
        
@router.get("/{company_id}/documents")
async def get_company_documents(
    company_id: str, 
    db: Session = Depends(get_db),
    include_ai_analysis: bool = True
) -> Dict[str, Any]:
    """Get all documents for a company with AI analysis from document vector data"""
    company = Company.get_company_by_id(db, UUID(company_id))
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    documents = []
    for doc in company.documents:
        documents.append({
            "id": str(doc.id),
            "file_name": doc.file_name,
            "document_type": doc.document_type,
            "file_size": doc.file_size,
            "upload_date": doc.upload_date,
            "processed": doc.processed,
            "company_id": str(doc.company_id)
        })
    
    response = {
        "company_id": company_id,
        "company_name": company.name,
        "total_documents": len(documents),
        "documents": documents
    }
    
    if include_ai_analysis and documents:
        try:
            document_context = company_rag_service.search_company_context(
                company_id=company_id,
                query="document content summary overview key points",
                k=15
            )
            
            document_chunks = [
                ctx for ctx in document_context 
                if ctx["metadata"].get("type") == "uploaded_document"
            ]
            
            if document_chunks:
                documents_summary = await generate_documents_summary(document_chunks, company.name)
                
                response["ai_analysis"] = {
                    "documents_summary": documents_summary.get("summary", "Cannot create summary"),
                    "key_topics": documents_summary.get("key_topics", []),
                    "document_insights": [
                        {
                            "content_preview": chunk["content"][:300] + "..." if len(chunk["content"]) > 300 else chunk["content"],
                        }
                        for chunk in document_chunks[:5]
                    ],
                    "total_chunks_analyzed": len(document_chunks)
                }
            else:
                response["ai_analysis"] = {
                    "documents_summary": "Documents have not been processed or not in vector store",
                    "key_topics": [],
                    "document_insights": [],
                    "total_chunks_analyzed": 0
                }
                
        except Exception as e:
            logger.error(f"Error generating document analysis for company {company_id}: {e}")
            response["ai_analysis"] = {
                "documents_summary": "Error analyzing documents",
                "key_topics": [],
                "document_insights": [],
                "error": str(e)
            }
    
    return response

async def generate_documents_summary(document_chunks: List[Dict], company_name: str) -> Dict:
    """Create summary based on actual document content"""
    if not document_chunks:
        return {"summary": "No documents to analyze", "key_topics": []}
    
    combined_content = []
    file_names = set()
    
    for chunk in document_chunks:
        combined_content.append(chunk["content"])
        if chunk["metadata"].get("file_name"):
            file_names.add(chunk["metadata"]["file_name"])
    
    content_text = "\n\n".join(combined_content)
    
    try:
        if company_rag_service.ai_available:
            prompt = f"""
            Analyze the following document content of {company_name} and create a summary:

            Document content:
            {content_text[:3000]}...

            Files analyzed: {', '.join(list(file_names))}

            Please return in JSON format:
            {{
                "summary": "A 2-3 sentence summary of the main content of the documents",
                "key_topics": ["Topic 1", "Topic 2", "Topic 3"]
            }}

            Only return JSON.
            """

            ai_response = company_rag_service.gemini_llm.invoke(prompt)
            return company_rag_service._parse_ai_response(ai_response.content) or {
                "summary": f"Documents contain technical information about {', '.join(list(file_names))}",
                "key_topics": list(file_names)
            }
        else:
            return {
                "summary": f"Documents contain information about {', '.join(list(file_names))}",
                "key_topics": list(file_names)
            }
            
    except Exception as e:
        logger.error(f"Error in AI analysis: {e}")
        return {
            "summary": f"Documents contain technical information, including {len(file_names)} files",
            "key_topics": list(file_names)
        }

@router.post("/{company_id}/keypoint-templates", response_model=KeypointTemplateResponse)
async def create_keypoint_template(
    company_id: str,
    template: KeypointTemplateCreate,
    db: Session = Depends(get_db)
):
    """
    Create keypoint template for company (HR Hunter setup)
    
    - Validate company exists
    - Create template for company
    - Return created template
    """
    try:
        company = Company.get_company_by_id(db, UUID(company_id))
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        if not template.name or template.name.strip() == "":
            raise HTTPException(status_code=400, detail="Template name is required")
        
        existing_template = db.query(KeypointTemplate).filter(
            KeypointTemplate.company_id == UUID(company_id),
            KeypointTemplate.name == template.name.strip(),
            KeypointTemplate.is_active == True
        ).first()
        
        if existing_template:
            raise HTTPException(
                status_code=400, 
                detail=f"Template with name '{template.name}' already exists for this company"
            )
        
        db_template = KeypointTemplate(
            company_id=UUID(company_id),
            name=template.name.strip(),
            description=template.description,
            position_type=template.position_type,
            industry=template.industry or company.industry,
            difficulty_level=template.difficulty_level,
            is_default=template.is_default,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        logger.info(f"Created keypoint template '{template.name}' for company {company_id}")
        
        return KeypointTemplateResponse(
            id=db_template.id,
            name=db_template.name,
            description=db_template.description,
            position_type=db_template.position_type,
            industry=db_template.industry,
            difficulty_level=db_template.difficulty_level,
            is_default=db_template.is_default,
            is_active=db_template.is_active,
            created_at=db_template.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating keypoint template for company {company_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create keypoint template: {str(e)}"
        )

@router.put("/{company_id}/keypoint-templates/{template_id}", response_model=KeypointTemplateResponse)
async def update_keypoint_template(
    company_id: str,
    template_id: str,
    template_update: KeypointTemplateCreate,
    db: Session = Depends(get_db)
):
    """Update keypoint template"""
    try:
        from app.models.keypoint_template import KeypointTemplate
        db_template = db.query(KeypointTemplate).filter(
            KeypointTemplate.id == UUID(template_id),
            KeypointTemplate.company_id == UUID(company_id),
            KeypointTemplate.is_active == True
        ).first()
        
        if not db_template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        existing = db.query(KeypointTemplate).filter(
            KeypointTemplate.company_id == UUID(company_id),
            KeypointTemplate.name == template_update.name.strip(),
            KeypointTemplate.id != UUID(template_id),
            KeypointTemplate.is_active == True
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"Template with name '{template_update.name}' already exists"
            )
        
        db_template.name = template_update.name.strip()
        db_template.description = template_update.description
        db_template.position_type = template_update.position_type
        db_template.industry = template_update.industry
        db_template.difficulty_level = template_update.difficulty_level
        db_template.is_default = template_update.is_default
        db_template.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(db_template)
        
        logger.info(f"Updated keypoint template {template_id}")
        
        return KeypointTemplateResponse(
            id=db_template.id,
            name=db_template.name,
            description=db_template.description,
            position_type=db_template.position_type,
            industry=db_template.industry,
            difficulty_level=db_template.difficulty_level,
            is_default=db_template.is_default,
            is_active=db_template.is_active,
            created_at=db_template.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template {template_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{company_id}/generate-keypoints", response_model=List[CompanyKeypointResponse])
async def generate_keypoints(
    company_id: str,
    count: int = 4,
    db: Session = Depends(get_db)
):
    """AI Generate 3-4 keypoints for company based on templates + company data"""
    try:
        company = Company.get_company_by_id(db, UUID(company_id))
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        template_latest = db.query(KeypointTemplate).filter(
            KeypointTemplate.company_id == UUID(company_id),
            KeypointTemplate.is_active == True
        ).order_by(KeypointTemplate.created_at.desc()).first()
        
        if not template_latest:
            raise HTTPException(status_code=404, detail="Template not found")
        
        generated_keypoints = []
        
        for i in range(min(count, 4)):
            ai_keypoint_data = company_rag_service.generate_multiple_keypoints_with_context(
                company_id=company_id,
                company=company,
                template=template_latest,
                position_type=template_latest.position_type,
                keypoint_index=i + 1,
                total_keypoints=count
            )
            
            db_keypoint = CompanyKeypoint.create_keypoint(db, {
                "company_id": UUID(company_id),
                "template_id": template_latest.id,
                "custom_name": ai_keypoint_data["custom_name"],
                "custom_description": ai_keypoint_data["custom_description"],
                "position_type": template_latest.position_type,
                "priority": i + 1,
                "source_data": ai_keypoint_data["source_data"],
                "generation_metadata": ai_keypoint_data["generation_metadata"]
            })
            
            keypoint_response = CompanyKeypointResponse(
                id=db_keypoint.id,
                company_id=db_keypoint.company_id,
                template_id=db_keypoint.template_id,
                custom_name=db_keypoint.custom_name,
                custom_description=db_keypoint.custom_description,
                position_type=db_keypoint.position_type,
                priority=db_keypoint.priority,
                order_index=db_keypoint.order_index,
                status=db_keypoint.status,
                is_active=db_keypoint.is_active,
                created_at=db_keypoint.created_at,
                template=KeypointTemplateResponse(
                    id=template_latest.id,
                    name=template_latest.name,
                    description=template_latest.description,
                    position_type=template_latest.position_type,
                    industry=template_latest.industry,
                    difficulty_level=template_latest.difficulty_level,
                    estimated_time_minutes=getattr(template_latest, 'estimated_time_minutes', 5),
                    is_default=template_latest.is_default,
                    is_active=template_latest.is_active,
                    created_at=template_latest.created_at
                ),
                questions=[],
                total_questions=0,
                estimated_total_time=0
            )
            
            generated_keypoints.append(keypoint_response)
        
        logger.info(f"Generated {len(generated_keypoints)} keypoints for company {company_id}")
        
        return generated_keypoints
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating keypoints: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to generate keypoints: {str(e)}")