# app/services/websocket/handle_session.py
import asyncio
import logging
from typing import Dict
import json
import redis
from app.core.config import settings

logger = logging.getLogger(__name__)

class InterviewSessionManager:
    def __init__(self):
        self.session_data: Dict[str, Dict] = {}
        self.redis_client = redis.from_url(settings.REDIS_URL)

    async def ensure_valid_session(self, session_id: str) -> bool:
        """
        Ensure session is valid: if it exists, load it, otherwise create a new one.
        """
        try:
            await self.get_session_data(session_id)
            return True
        except Exception as e:
            logger.error(f"Error ensuring session {session_id}: {e}")
            return False
        
    async def get_session_data(self, session_id: str) -> Dict:
        """
        Get session data
        """
        session = self.redis_client.get(f"session:{session_id}")
        if session:
            return json.loads(session)
        else:
            return await self.create_session(session_id)
    
    async def update_session_data(self, session_id: str, data: Dict):
        """
        Update session data
        """
        session = await self.get_session_data(session_id)
        session.update(data)
        self.redis_client.set(f"session:{session_id}", json.dumps(session))

    async def create_session(self, session_id: str) -> Dict:
        """
        Create a new session
        """
        if session_id not in self.session_data:
            session = {
                "company_id": "",
                "status": "created",
                "conversation_history": [],
                "current_stage": "introduction",
                "candidate_profile": {},
                "question_count": 0,
                "last_activity": asyncio.get_event_loop().time(),
                "created_fresh": True
            }
            self.session_data[session_id] = session
            session["skills_detected"] = []
            self.redis_client.set(f"session:{session_id}", json.dumps(session))
            return session
        
        return self.session_data[session_id]
    
    def cleanup_session_cache(self, session_id: str):
        """Cleanup session cache"""
        try:
            self.redis_client.delete(f"session:{session_id}")
        except Exception as e:
            logger.error(f"Error cleaning up session cache: {e}")
            