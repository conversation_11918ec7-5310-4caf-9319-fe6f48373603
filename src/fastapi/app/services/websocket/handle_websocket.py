# app/services/websocket/handle_websocket.py
from fastapi import WebSocket
from typing import Dict, List, Optional
import asyncio
from datetime import datetime
import logging

from app.services.websocket.utils import WebSocketUtils
from app.services.websocket.handle_session import InterviewSessionManager
from app.services.websocket.protocols import WebSocketHandlerProtocol

logger = logging.getLogger(__name__)

class WebSocketHandler:
    """WebSocket handler implementation with proper dependency injection"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict] = {}
        
        self.rate_limits: Dict[str, List[float]] = {}
        self.rate_limit_window = 60
        self.max_requests_per_window = 30
        
        self.utils = WebSocketUtils(self)
        self.handle_session = InterviewSessionManager()

        from app.services.websocket.flow_interview import InterviewFlow
        self.flow_interview = InterviewFlow()
        self.flow_interview.set_message_utils(self.utils)
        
        
        logger.info("WebSocket<PERSON>andler initialized successfully")
    
    async def connect(self, websocket: WebSocket, session_id: str) -> bool:
        """Establish WebSocket connection with comprehensive monitoring"""
        try:
            await websocket.accept()

            self.active_connections[session_id] = websocket
            self.connection_metadata[session_id] = {
                "connected_at": datetime.utcnow(),
                "last_activity": datetime.utcnow(),
                "message_count": 0,
                "bytes_sent": 0,
                "bytes_received": 0,
                "connection_quality": "excellent",
                "client_info": {
                    "user_agent": websocket.headers.get("user-agent", "unknown"),
                    "origin": websocket.headers.get("origin", "unknown")
                }
            }
            
            await self.utils.start_heartbeat_monitor(session_id)
            
            logger.info(f"Connection established successfully for {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to establish connection {session_id}: {e}")
            await self._cleanup_failed_connection(session_id)
            return False
    
    async def disconnect(self, session_id: str) -> None:
        """Disconnect and cleanup comprehensive"""
        try:
            logger.info(f"Disconnecting session {session_id}")
            await self.utils.stop_heartbeat_monitor(session_id)
            
            if session_id in self.active_connections:
                try:
                    websocket = self.active_connections[session_id]
                    await websocket.close()
                except Exception as e:
                    logger.warning(f"Error closing websocket for {session_id}: {e}")
                finally:
                    del self.active_connections[session_id]
            
            self.connection_metadata.pop(session_id, None)
            self.rate_limits.pop(session_id, None)
            self.handle_session.cleanup_session_cache(session_id)
            
            logger.info(f"Session {session_id} disconnected and cleaned up")
        except Exception as e:
            logger.error(f"Error during disconnection {session_id}: {e}")
    
    async def update_connection_metadata(self, session_id: str, data: Dict) -> None:
        """Update connection metadata"""
        try:
            if session_id not in self.connection_metadata:
                logger.warning(f"Metadata not found for session {session_id}")
                return
            
            self.connection_metadata[session_id]["last_activity"] = datetime.utcnow()
            
            for key, value in data.items():
                if key == "bytes_sent":
                    self.connection_metadata[session_id]["bytes_sent"] += value
                elif key == "bytes_received":
                    self.connection_metadata[session_id]["bytes_received"] += value
                elif key in ["connection_quality", "client_info"]:
                    self.connection_metadata[session_id][key] = value
                else:
                    self.connection_metadata[session_id][key] = value
            
            logger.debug(f"Updated metadata for {session_id}: {data}")
            
        except Exception as e:
            logger.error(f"Error updating metadata for {session_id}: {e}")
    
    async def _cleanup_failed_connection(self, session_id: str) -> None:
        """Cleanup after failed connection"""
        try:
            self.active_connections.pop(session_id, None)
            self.connection_metadata.pop(session_id, None)
            self.rate_limits.pop(session_id, None)
        except Exception as e:
            logger.error(f"Error cleaning up failed connection {session_id}: {e}")
    
    def get_connection_stats(self) -> Dict:
        """Get connection statistics"""
        total_connections = len(self.active_connections)
        total_messages = sum(
            metadata.get("message_count", 0) 
            for metadata in self.connection_metadata.values()
        )
        
        return {
            "total_active_connections": total_connections,
            "total_messages_sent": total_messages,
            "connections": {
                session_id: {
                    "connected_duration": (
                        datetime.utcnow() - metadata["connected_at"]
                    ).total_seconds(),
                    "message_count": metadata.get("message_count", 0),
                    "bytes_sent": metadata.get("bytes_sent", 0),
                    "connection_quality": metadata.get("connection_quality", "unknown")
                }
                for session_id, metadata in self.connection_metadata.items()
            }
        }
        
    async def check_rate_limit(self, session_id: str) -> bool:
        """Check rate limiting for session"""
        try:
            current_time = datetime.utcnow().timestamp()    
            
            if session_id not in self.rate_limits:
                self.rate_limits[session_id] = []
            
            cutoff_time = current_time - self.rate_limit_window
            self.rate_limits[session_id] = [
                req_time for req_time in self.rate_limits[session_id]
                if req_time > cutoff_time
            ]
            
            if len(self.rate_limits[session_id]) >= self.max_requests_per_window:
                logger.warning(
                    f"Rate limit exceeded for {session_id}: "
                    f"{len(self.rate_limits[session_id])} requests in last {self.rate_limit_window}s"
                )
                return False
            
            self.rate_limits[session_id].append(current_time)
            
            logger.debug(
                f"Rate limit check passed for {session_id}: "
                f"{len(self.rate_limits[session_id])}/{self.max_requests_per_window} requests"
            )
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {session_id}: {e}")
            return True
    
    async def process_video_frame(self, session_id: str, frame_data: bytes) -> bool:
        """Process a video frame from the candidate"""
        return await self.video_service.save_video_frame(session_id, frame_data)
    
    async def start_video_recording(self, session_id: str) -> bool:
        """Start recording video for a session"""
        return await self.video_service.start_recording(session_id)
    
    async def stop_video_recording(self, session_id: str) -> Optional[str]:
        """Stop recording video for a session"""
        return await self.video_service.stop_recording(session_id)

