# app/services/websocket/utils.py
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from .protocols import WebSocketHandlerProtocol

logger = logging.getLogger(__name__)

class WebSocketUtils:
    """Utility class for WebSocket operations with dependency injection"""
    
    def __init__(self, handler: WebSocketHandlerProtocol):
        self._handler = handler
        self._heartbeat_tasks: Dict[str, asyncio.Task] = {}
    
    @property
    def handler(self) -> WebSocketHandlerProtocol:
        """Getter for handler to ensure type safety"""
        return self._handler
    
    async def send_message(self, session_id: str, message: Dict[str, Any], 
        message_type: str = "message", **kwargs) -> bool:
        """
        Send message to specific session with error handling
        
        Args:
            session_id: ID of session
            message: Dictionary containing message data
            
        Returns:
            bool: True if message sent successfully, False if failed
        """
        if session_id not in self.handler.active_connections:
            logger.warning(f"Session {session_id} not found in active connections")
            return False
            
        try:
            websocket = self.handler.active_connections[session_id]
            message_json = json.dumps({
                "type": message_type,
                "message": message,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            })
            await websocket.send_text(message_json)
            
            await self.handler.update_connection_metadata(session_id, {
                "bytes_sent": len(message_json.encode('utf-8')),
                "connection_quality": "excellent"
            })
            
            if session_id in self.handler.connection_metadata:
                self.handler.connection_metadata[session_id]["message_count"] += 1
            
            logger.debug(f"Message sent successfully to {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {session_id}: {e}")
            await self.handler.disconnect(session_id)
            return False
    
    async def send_error(self, session_id: str, error_message: str) -> bool:
        """
        Send error message to session
        
        Args:
            session_id: ID of session
            error_message: Error message string
            
        Returns:
            bool: True if message sent successfully, False if failed
        """
        error_payload = {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.utcnow().isoformat()
        }
        return await self.send_message(session_id, error_payload)
    
    async def send_system_message(self, session_id: str, message: str, **kwargs) -> bool:
        """
        Send system message to session
        
        Args:
            session_id: ID of session
            message: System message content
            **kwargs: Additional data for message
            
        Returns:
            bool: True if message sent successfully, False if failed
        """
        system_payload = {
            "type": "system_message",
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        return await self.send_message(session_id, system_payload)
    
    async def start_heartbeat_monitor(self, session_id: str, interval: int = 30) -> None:
        """
        Start heartbeat monitoring for session
        
        Args:
            session_id: ID of session
            interval: Interval between pings (seconds)
        """
        if session_id in self._heartbeat_tasks:
            logger.warning(f"Heartbeat already running for {session_id}")
            return
            
        task = asyncio.create_task(
            self._heartbeat_loop(session_id, interval)
        )
        self._heartbeat_tasks[session_id] = task
        logger.info(f"Started heartbeat monitor for {session_id}")
    
    async def stop_heartbeat_monitor(self, session_id: str) -> None:
        """
        Stop heartbeat monitoring for session
        
        Args:
            session_id: ID of session
        """
        if session_id in self._heartbeat_tasks:
            self._heartbeat_tasks[session_id].cancel()
            del self._heartbeat_tasks[session_id]
            logger.info(f"Stopped heartbeat monitor for {session_id}")
    
    async def _heartbeat_loop(self, session_id: str, interval: int) -> None:
        """
        Internal heartbeat loop
        
        Args:
            session_id: ID of session
            interval: Ping interval
        """
        while session_id in self.handler.active_connections:
            try:
                await asyncio.sleep(interval)
                
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                success = await self.send_message(session_id, ping_message)
                if not success:
                    logger.warning(f"Heartbeat ping failed for {session_id}")
                    break
                    
            except asyncio.CancelledError:
                logger.info(f"Heartbeat cancelled for {session_id}")
                break
            except Exception as e:
                logger.error(f"Heartbeat error for {session_id}: {e}")
                break
        
        # Cleanup when loop ends
        await self.stop_heartbeat_monitor(session_id)
    
    async def broadcast_message(self, message: Dict[str, Any], exclude_sessions: Optional[list] = None) -> int:
        """
        Broadcast message to all active connections
        
        Args:
            message: Message to broadcast
            exclude_sessions: List of session_id to exclude
            
        Returns:
            int: Number of sessions that received message successfully
        """
        exclude_sessions = exclude_sessions or []
        success_count = 0
        
        for session_id in list(self.handler.active_connections.keys()):
            if session_id not in exclude_sessions:
                if await self.send_message(session_id, message):
                    success_count += 1
        
        logger.info(f"Broadcasted message to {success_count} sessions")
        return success_count
