# src/fastapi/app/services/websocketv2/video_enhanced_handler.py

import json
import base64
import logging
from datetime import datetime
from typing import Dict, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from app.services.websocket.handle_websocket import WebSocketHandler
from app.services.video.video_segment_service import VideoSegmentService
from app.services.ai.gemini_video_analyzer import GeminiVideoAnalyzer
from app.models.video_analysis import VideoSegment, VideoAnalysis
from app.models.question import Question
from app.models.base import SessionLocal

logger = logging.getLogger(__name__)

class VideoEnhancedWebSocketHandler(WebSocketHandler):
    def __init__(self):
        super().__init__()
        self.video_segment_service = VideoSegmentService()
        self.gemini_video_analyzer = GeminiVideoAnalyzer()
        
    def _get_db_session(self) -> Session:
        """Get database session"""
        return SessionLocal()
        
    async def handle_video_message(self, session_id: str, message_data: Dict) -> None:
        """Handle video-related WebSocket messages"""
        message_type = message_data.get("type")
        
        try:
            if message_type == "start_video_recording":
                await self._handle_start_video_recording(session_id, message_data)
                
            elif message_type == "video_chunk":
                await self._handle_video_chunk(session_id, message_data)
                
            elif message_type == "end_video_recording":
                await self._handle_end_video_recording(session_id, message_data)
                
            elif message_type == "answer_completed":
                await self._handle_answer_completion(session_id, message_data)
                
            else:
                logger.warning(f"Unknown video message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling video message: {e}")
            await self.utils.send_error(session_id, f"Video processing error: {str(e)}")
    
    async def _handle_start_video_recording(self, session_id: str, message_data: Dict) -> None:
        """Handle start video recording request"""
        try:
            question_id = UUID(message_data["question_id"])
            interview_session_id = UUID(message_data["interview_session_id"])
            
            # Get database session
            db = self._get_db_session()
            
            try:
                # Start new video segment
                segment = await self.video_segment_service.start_question_segment(
                    db=db,
                    session_id=session_id,
                    question_id=question_id,
                    interview_session_id=interview_session_id
                )
                
                # Send confirmation
                await self.utils.send_message(session_id, {
                    "type": "video_recording_started",
                    "segment_id": str(segment.id),
                    "question_id": str(question_id),
                    "timestamp": datetime.utcnow().isoformat()
                })
                
                logger.info(f"Video recording started for session {session_id}, question {question_id}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to start video recording: {e}")
            await self.utils.send_error(session_id, "Failed to start video recording")
    
    async def _handle_video_chunk(self, session_id: str, message_data: Dict) -> None:
        """Handle incoming video chunk"""
        try:
            chunk_data = message_data["chunk_data"]  # base64 encoded
            timestamp_str = message_data.get("timestamp")
            
            timestamp = datetime.fromisoformat(timestamp_str) if timestamp_str else datetime.utcnow()
            
            # Add chunk to current segment
            success = await self.video_segment_service.add_video_chunk(
                session_id=session_id,
                chunk_data=chunk_data,
                timestamp=timestamp
            )
            
            if success:
                # Update connection metadata
                segment_info = self.video_segment_service.get_segment_info(session_id)
                if segment_info:
                    await self.update_connection_metadata(session_id, {
                        "video_chunks_received": segment_info["chunk_count"],
                        "video_data_size": segment_info["total_size"]
                    })
            else:
                logger.warning(f"Failed to add video chunk for session {session_id}")
                
        except Exception as e:
            logger.error(f"Failed to handle video chunk: {e}")
    
    async def _handle_end_video_recording(self, session_id: str, message_data: Dict) -> None:
        """Handle end video recording request"""
        try:
            # Get database session
            db = self._get_db_session()
            
            try:
                # End current segment
                result = await self.video_segment_service.end_current_segment(db, session_id)
                
                if result:
                    segment, video_data = result
                    
                    # Send confirmation
                    await self.utils.send_message(session_id, {
                        "type": "video_recording_ended",
                        "segment_id": str(segment.id),
                        "duration_seconds": segment.duration_seconds,
                        "video_size_bytes": len(video_data),
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
                    logger.info(f"Video recording ended for session {session_id}")
                else:
                    await self.utils.send_error(session_id, "No active video recording found")
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to end video recording: {e}")
            await self.utils.send_error(session_id, "Failed to end video recording")
    
    async def _handle_answer_completion(self, session_id: str, message_data: Dict) -> None:
        """Handle answer completion with video analysis"""
        try:
            question_id = UUID(message_data["question_id"])
            text_answer = message_data.get("text_answer", "")
            
            # Get database session
            db = self._get_db_session()
            
            try:
                # End video segment and get video data
                result = await self.video_segment_service.end_current_segment(db, session_id)
                
                if not result:
                    # No video data, process as text-only
                    await self._process_text_only_answer(session_id, question_id, text_answer, db)
                    return
                
                segment, video_data = result
                
                # Send processing status
                await self.utils.send_message(session_id, {
                    "type": "processing_status",
                    "message": "Đang phân tích video của bạn với AI...",
                    "stage": "video_analysis"
                })
                
                # Get question context
                question = db.query(Question).filter(Question.id == question_id).first()
                if not question:
                    logger.error(f"Question not found: {question_id}")
                    await self.utils.send_error(session_id, "Question not found")
                    return
                
                # Analyze video with AI
                analysis_result = await self.gemini_video_analyzer.analyze_question_segment(
                    video_data=video_data,
                    question=question,
                    segment=segment
                )
                
                # Store analysis in database
                video_analysis = VideoAnalysis.create_analysis(
                    db=db,
                    video_segment=segment,
                    gemini_result=analysis_result,
                    scores=analysis_result.get("scores", {}),
                    behavioral_insights=analysis_result.get("behavioral_insights", {}),
                    recommendations=analysis_result.get("recommendations", {})
                )
                
                # Send comprehensive results
                await self.utils.send_message(session_id, {
                    "type": "answer_analysis_complete",
                    "question_id": str(question_id),
                    "video_analysis": {
                        "analysis_id": str(video_analysis.id),
                        "scores": analysis_result.get("scores", {}),
                        "insights": analysis_result.get("behavioral_insights", {}),
                        "feedback": analysis_result.get("recommendations", {}).get("immediate_feedback", ""),
                        "strengths": analysis_result.get("detailed_analysis", {}).get("strengths", []),
                        "improvements": analysis_result.get("detailed_analysis", {}).get("areas_for_improvement", [])
                    },
                    "text_analysis": {
                        "content": text_answer,
                        "length": len(text_answer),
                        "word_count": len(text_answer.split()) if text_answer else 0
                    },
                    "combined_evaluation": await self._create_combined_evaluation(
                        question, text_answer, analysis_result
                    ),
                    "timestamp": datetime.utcnow().isoformat()
                })
                
                logger.info(f"Video analysis completed for question {question_id}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to process answer completion: {e}")
            await self.utils.send_error(session_id, "Failed to analyze your response")
    
    async def _process_text_only_answer(
        self, 
        session_id: str, 
        question_id: UUID, 
        text_answer: str, 
        db: Session
    ) -> None:
        """Process answer without video analysis"""
        try:
            # Send basic text analysis
            await self.utils.send_message(session_id, {
                "type": "answer_analysis_complete",
                "question_id": str(question_id),
                "video_analysis": None,
                "text_analysis": {
                    "content": text_answer,
                    "length": len(text_answer),
                    "word_count": len(text_answer.split()) if text_answer else 0
                },
                "combined_evaluation": {
                    "overall_score": 7.0,
                    "notes": "Text-only evaluation - video analysis not available"
                },
                "timestamp": datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Failed to process text-only answer: {e}")
    
    async def _create_combined_evaluation(
        self, 
        question: Question, 
        text_answer: str, 
        video_analysis: Dict
    ) -> Dict:
        """Create combined evaluation from text and video analysis"""
        try:
            video_scores = video_analysis.get("scores", {})
            
            # Calculate weighted scores based on question type
            if question.question_type == "behavioral":
                weights = {"content": 0.3, "video": 0.7}
            elif question.question_type == "technical":
                weights = {"content": 0.6, "video": 0.4}
            else:
                weights = {"content": 0.4, "video": 0.6}
            
            # Simple content scoring (can be enhanced)
            content_score = min(10, len(text_answer.split()) / 10) if text_answer else 0
            
            # Video average score
            video_score = sum(video_scores.values()) / len(video_scores) if video_scores else 0
            
            # Combined score
            combined_score = (content_score * weights["content"] + video_score * weights["video"])
            
            return {
                "overall_score": round(combined_score, 2),
                "content_score": round(content_score, 2),
                "video_score": round(video_score, 2),
                "weights_used": weights,
                "evaluation_method": "text_video_combined"
            }
            
        except Exception as e:
            logger.error(f"Failed to create combined evaluation: {e}")
            return {"overall_score": 7.0, "notes": "Evaluation error"}
