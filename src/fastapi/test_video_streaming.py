#!/usr/bin/env python3
"""
Test script for video streaming flow with Gemini analysis
"""
import asyncio
import json
import base64
import logging
from datetime import datetime
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoStreamingTest:
    def __init__(self):
        self.test_session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.test_question_id = "test_question_123"
        self.video_chunks = []
        
    async def test_video_streaming_flow(self):
        """Test complete video streaming flow"""
        logger.info("🚀 Starting video streaming flow test")
        
        try:
            # Step 1: Start video recording
            await self.test_start_video_recording()
            
            # Step 2: Send video chunks
            await self.test_send_video_chunks()
            
            # Step 3: End video recording
            await self.test_end_video_recording()
            
            # Step 4: Test answer completion
            await self.test_answer_completion()
            
            logger.info("✅ Video streaming flow test completed successfully")
            
        except Exception as e:
            logger.error(f"❌ Video streaming flow test failed: {e}")
            raise
    
    async def test_start_video_recording(self):
        """Test start video recording"""
        logger.info("📹 Testing start video recording")
        
        message = {
            "type": "start_video_recording",
            "question_id": self.test_question_id,
            "session_id": self.test_session_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Simulate sending to WebSocket
        logger.info(f"📤 Sending start recording message: {json.dumps(message, indent=2)}")
        
        # Expected response
        expected_response = {
            "type": "video_recording_started",
            "segment_id": "test_segment_123",
            "question_id": self.test_question_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"📥 Expected response: {json.dumps(expected_response, indent=2)}")
    
    async def test_send_video_chunks(self):
        """Test sending video chunks"""
        logger.info("🎬 Testing video chunk sending")
        
        # Generate test video chunks
        for i in range(5):
            chunk_data = self.generate_test_video_chunk(i)
            
            message = {
                "type": "video_chunk",
                "chunk_data": chunk_data,
                "timestamp": datetime.utcnow().isoformat(),
                "frame_index": i,
                "quality": 85,
                "question_id": self.test_question_id,
                "session_id": self.test_session_id,
            }
            
            logger.info(f"📤 Sending video chunk {i+1}/5: {len(chunk_data)} bytes")
            
            # Store for later use
            self.video_chunks.append(message)
        
        logger.info(f"✅ Sent {len(self.video_chunks)} video chunks")
    
    async def test_end_video_recording(self):
        """Test end video recording"""
        logger.info("🛑 Testing end video recording")
        
        message = {
            "type": "end_video_recording",
            "question_id": self.test_question_id,
            "session_id": self.test_session_id,
            "duration": 5,
            "frame_count": len(self.video_chunks),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"📤 Sending end recording message: {json.dumps(message, indent=2)}")
        
        # Expected response
        expected_response = {
            "type": "video_recording_ended",
            "segment_id": "test_segment_123",
            "duration_seconds": 5,
            "video_size_bytes": sum(len(chunk["chunk_data"]) for chunk in self.video_chunks),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"📥 Expected response: {json.dumps(expected_response, indent=2)}")
    
    async def test_answer_completion(self):
        """Test answer completion with video analysis"""
        logger.info("🎯 Testing answer completion with video analysis")
        
        message = {
            "type": "answer_completed",
            "question_id": self.test_question_id,
            "session_id": self.test_session_id,
            "text_answer": "Tôi có kinh nghiệm 5 năm trong lĩnh vực phát triển phần mềm. Tôi đã làm việc với nhiều công nghệ khác nhau và có khả năng học hỏi nhanh chóng.",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"📤 Sending answer completion message: {json.dumps(message, indent=2)}")
        
        # Expected processing status
        processing_status = {
            "type": "processing_status",
            "message": "Đang phân tích video của bạn với AI...",
            "stage": "video_analysis"
        }
        
        logger.info(f"📥 Expected processing status: {json.dumps(processing_status, indent=2)}")
        
        # Expected analysis result
        expected_analysis = {
            "type": "answer_analysis_complete",
            "question_id": self.test_question_id,
            "video_analysis": {
                "analysis_id": "analysis_123",
                "scores": {
                    "communication_effectiveness": 8.5,
                    "non_verbal_communication": 7.8,
                    "content_quality": 8.2,
                    "overall_impression": 8.1,
                    "question_specific_score": 8.0
                },
                "insights": {
                    "confidence_level": "high",
                    "engagement_quality": "excellent",
                    "authenticity": "genuine",
                    "stress_indicators": "minimal",
                    "communication_style": "confident"
                },
                "feedback": "Câu trả lời rất tốt! Bạn thể hiện sự tự tin và có cấu trúc rõ ràng.",
                "strengths": [
                    "Giao tiếp rõ ràng và tự tin",
                    "Có ví dụ cụ thể về kinh nghiệm",
                    "Thể hiện khả năng học hỏi"
                ],
                "improvements": [
                    "Có thể thêm chi tiết về các dự án cụ thể",
                    "Thể hiện kết quả định lượng nếu có"
                ]
            },
            "text_analysis": {
                "content": message["text_answer"],
                "length": len(message["text_answer"]),
                "word_count": len(message["text_answer"].split())
            },
            "combined_evaluation": {
                "overall_score": 8.1,
                "content_score": 8.2,
                "video_score": 8.0,
                "weights_used": {
                    "content": 0.4,
                    "video": 0.6
                },
                "evaluation_method": "text_video_combined"
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"📥 Expected analysis result: {json.dumps(expected_analysis, indent=2)}")
    
    def generate_test_video_chunk(self, index: int) -> str:
        """Generate test video chunk data"""
        # Simulate video frame data (base64 encoded)
        test_data = f"test_video_frame_{index}_{datetime.now().timestamp()}"
        return base64.b64encode(test_data.encode()).decode()
    
    def print_flow_summary(self):
        """Print flow summary"""
        logger.info("\n" + "="*60)
        logger.info("📊 VIDEO STREAMING FLOW SUMMARY")
        logger.info("="*60)
        
        flow_steps = [
            "1. Candidate Starts Interview",
            "2. Establish WebSocket Connection",
            "3. Start Video Capture",
            "4. Capture Video Frames",
            "5. Encode Frames",
            "6. Send via WebSocket",
            "7. Backend Frame Reception",
            "8. Frame Validation",
            "9. Buffer Frames for Question",
            "10. Finalize Question Video Segment",
            "11. Prepare Video for Gemini API",
            "12. Send Video Segment to Gemini",
            "13. Gemini Analysis Processing",
            "14. Receive Analysis Result",
            "15. Process Gemini Response",
            "16. Extract Analysis Data",
            "17. Send Analysis to Frontend",
            "18. Update UI Indicators",
            "19. Store Question Analysis",
            "20. Update Interview Context"
        ]
        
        for step in flow_steps:
            logger.info(f"✅ {step}")
        
        logger.info("\n🎯 Key Components:")
        logger.info("   • Frontend: SelfVideoView, GoogleMeetInterview")
        logger.info("   • Backend: VideoEnhancedWebSocketHandler, VideoSegmentService")
        logger.info("   • AI: GeminiVideoAnalyzer")
        logger.info("   • Database: VideoSegment, VideoAnalysis models")
        
        logger.info("\n🚀 Flow Status: READY FOR TESTING")
        logger.info("="*60)

async def main():
    """Main test function"""
    test = VideoStreamingTest()
    
    # Print flow summary
    test.print_flow_summary()
    
    # Run tests
    await test.test_video_streaming_flow()
    
    logger.info("\n🎉 All tests completed successfully!")

if __name__ == "__main__":
    asyncio.run(main()) 