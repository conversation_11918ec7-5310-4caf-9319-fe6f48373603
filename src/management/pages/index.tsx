import { 
  Box, 
  Container, 
  Heading, 
  VStack, 
  HStack, 
  Text, 
  Icon, 
  useColorModeValue,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  SimpleGrid,
  Badge,
  Button,
  Divider,
} from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON><PERSON>ider } from '@chakra-ui/react';
import { QuestionScriptGenerator } from '../components/QuestionScriptGenerator';
import { QuestionList } from '../components/QuestionList';
import { MeetingLinkGenerator } from '../components/MeetingLinkGenerator';
import { FeedbackViewer } from '../components/FeedbackViewer';
import { AgentDashboard } from '../components/AgentDashboard';
import { DocumentUploader } from '../components/DocumentUploader';
import { InterviewIntentConfig } from '../components/InterviewIntentConfig';
import { IntegratedLinkWorkflow } from '../components/IntegratedLinkWorkflow';
import { LinkManager } from '../components/LinkManager';
import { 
  SettingsIcon, 
  ViewIcon, 
  LinkIcon, 
  EditIcon, 
  AttachmentIcon, 
  QuestionIcon,
  StarIcon,
  TimeIcon,
  CheckCircleIcon,
  InfoIcon
} from '@chakra-ui/icons';
import { useState } from 'react';
import { UnifiedTabList } from '../components/shared/UnifiedCard';

interface Question {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number;
}

export default function Home() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');

  const handleQuestionsGenerated = (newQuestions: Question[]) => {
    setQuestions(newQuestions);
  };


  return (
    <ChakraProvider>
      <Box bg={bgColor} minH="100vh">
        {/* ヘッダー */}
        <Box bg={cardBg} borderBottomWidth="1px" borderBottomColor="gray.200" py={4}>
          <HStack justify="center" spacing={3} px={4}>
            <Icon as={StarIcon} color="blue.500" boxSize={6} />
            <Heading size="lg" color="blue.600">面接くん 管理画面</Heading>
          </HStack>
        </Box>

        {/* メインコンテンツ */}
        <Box maxW="7xl" mx="auto" p={4} width="100%">
          <Tabs index={activeTab} onChange={setActiveTab} variant="soft-rounded" colorScheme="blue">
            <UnifiedTabList width="100%">
              <TabList 
                mb={4} 
                overflowX="auto" 
                flexWrap="nowrap"
                width="100%"
              >
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={SettingsIcon} mr={2} boxSize={4} />
                ダッシュボード
              </Tab>
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={LinkIcon} mr={2} boxSize={4} />
                リンク作成
              </Tab>
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={SettingsIcon} mr={2} boxSize={4} />
                リンク管理
              </Tab>
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={ViewIcon} mr={2} boxSize={4} />
                分析結果
              </Tab>
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={EditIcon} mr={2} boxSize={4} />
                質問管理
              </Tab>
              <Tab 
                fontSize={{ base: "xs", sm: "sm", md: "md" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 3, md: 4 }}
                minW="fit-content"
                whiteSpace="nowrap"
                flex="none"
                _selected={{
                  bg: "blue.50",
                  borderColor: "blue.500",
                }}
              >
                <Icon as={AttachmentIcon} mr={2} boxSize={4} />
                資料管理
              </Tab>
              </TabList>
            </UnifiedTabList>

            <TabPanels>
              <TabPanel p={0}>
                <VStack spacing={4} width="100%">
                  <AgentDashboard />
                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} w="100%">
                    <Button leftIcon={<LinkIcon />} onClick={() => setActiveTab(1)} size="lg" w="100%">
                      統合リンク作成
                    </Button>
                    <Button leftIcon={<SettingsIcon />} onClick={() => setActiveTab(2)} size="lg" w="100%">
                      リンク管理
                    </Button>
                    <Button leftIcon={<ViewIcon />} onClick={() => setActiveTab(3)} size="lg" w="100%">
                      分析結果確認
                    </Button>
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              <TabPanel p={0}>
                <Box width="100%">
                  <IntegratedLinkWorkflow 
                    onLinkGenerated={(linkData) => {
                      console.log('リンク生成完了:', linkData);
                      setActiveTab(2);
                    }}
                  />
                </Box>
              </TabPanel>

              <TabPanel p={0}>
                <Box width="100%">
                  <LinkManager />
                </Box>
              </TabPanel>

              <TabPanel p={0}>
                <Box width="100%">
                  <FeedbackViewer />
                </Box>
              </TabPanel>

              <TabPanel p={0}>
                <VStack spacing={6} width="100%">
                  <InterviewIntentConfig />
                  <QuestionScriptGenerator onQuestionsGenerated={handleQuestionsGenerated} />
                  {questions.length > 0 && <QuestionList questions={questions} />}
                </VStack>
              </TabPanel>

              <TabPanel p={0}>
                <Box width="100%">
                  <DocumentUploader />
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      </Box>
    </ChakraProvider>
  );
} 