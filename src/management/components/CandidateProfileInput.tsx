'use client';

/**
 * 候補者プロファイル入力コンポーネント
 * MeetingLinkGenerator用の簡易プロファイル入力フォーム
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  Textarea,
  VStack,
  HStack,
  Text,
  Badge,
  useToast,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  SimpleGrid,
  IconButton,
  useColorModeValue,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, CheckIcon } from '@chakra-ui/icons';
import { CandidateProfile, PersonalInfo, SkillInfo, WorkExperience } from '@mensetsu-kun/shared/types/candidate-profile';

interface CandidateProfileInputProps {
  onProfileSet: (profile: CandidateProfile | null) => void;
  currentProfile?: CandidateProfile | null;
  simplified?: boolean; // 簡易版フラグ
}

function CandidateProfileInput({
  onProfileSet,
  currentProfile,
  simplified = true,
}: CandidateProfileInputProps) {
  const [profile, setProfile] = useState<Partial<CandidateProfile>>({
    personalInfo: {
      name: '',
      email: '',
      currentPosition: '',
      yearsOfExperience: 0,
    },
    workExperience: [],
    skills: [],
    education: [],
    preferences: {
      communicationStyle: 'balanced',
      feedbackPreference: 'constructive',
      pacePreference: 'normal',
    },
  });

  const [tempSkill, setTempSkill] = useState('');
  const [tempExperience, setTempExperience] = useState({ company: '', position: '', duration: '' });
  
  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // 現在のプロファイルから初期化
  useEffect(() => {
    if (currentProfile) {
      setProfile(currentProfile);
    }
  }, [currentProfile]);

  const handlePersonalInfoChange = (field: string, value: string | number) => {
    setProfile(prev => ({
      ...prev,
      personalInfo: {
        name: prev.personalInfo?.name || '',
        email: prev.personalInfo?.email || '',
        phone: prev.personalInfo?.phone,
        location: prev.personalInfo?.location,
        nationality: prev.personalInfo?.nationality,
        birthDate: prev.personalInfo?.birthDate,
        currentPosition: prev.personalInfo?.currentPosition,
        yearsOfExperience: prev.personalInfo?.yearsOfExperience,
        ...prev.personalInfo,
        [field]: value,
      } as PersonalInfo,
    }));
  };

  const handlePreferencesChange = (field: string, value: string) => {
    setProfile(prev => ({
      ...prev,
      preferences: {
        communicationStyle: prev.preferences?.communicationStyle || 'balanced',
        feedbackPreference: prev.preferences?.feedbackPreference || 'constructive',
        pacePreference: prev.preferences?.pacePreference || 'normal',
        ...prev.preferences,
        [field]: value,
      },
    }));
  };

  const addSkill = () => {
    if (tempSkill.trim()) {
      setProfile(prev => ({
        ...prev,
        skills: [...(prev.skills || []), {
          name: tempSkill.trim(),
          category: 'technical', // デフォルトは技術スキルとして追加
          level: 'intermediate',
          yearsOfExperience: undefined,
        } as SkillInfo],
      }));
      setTempSkill('');
    }
  };

  const removeSkill = (index: number) => {
    setProfile(prev => ({
      ...prev,
      skills: prev.skills?.filter((_, i) => i !== index) || [],
    }));
  };

  const addExperience = () => {
    if (tempExperience.company && tempExperience.position) {
      setProfile(prev => ({
        ...prev,
        workExperience: [...(prev.workExperience || []), {
          id: `exp_${Date.now()}`,
          company: tempExperience.company,
          position: tempExperience.position,
          startDate: tempExperience.duration || '',
          isCurrentRole: false,
          responsibilities: [],
          achievements: [],
          skills: [],
        } as WorkExperience],
      }));
      setTempExperience({ company: '', position: '', duration: '' });
    }
  };

  const removeExperience = (index: number) => {
    setProfile(prev => ({
      ...prev,
      workExperience: prev.workExperience?.filter((_, i) => i !== index) || [],
    }));
  };

  const handleSaveProfile = () => {
    // バリデーション
    if (!profile.personalInfo?.name) {
      toast({
        title: 'エラー',
        description: '候補者名は必須です',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    // プロファイルを完成させる
    const completeProfile: CandidateProfile = {
      id: currentProfile?.id || `profile_${Date.now()}`,
      personalInfo: {
        name: profile.personalInfo.name,
        email: profile.personalInfo?.email || '',
        phone: profile.personalInfo?.phone || '',
        currentPosition: profile.personalInfo?.currentPosition || '',
        yearsOfExperience: profile.personalInfo?.yearsOfExperience || 0,
      },
      workExperience: profile.workExperience || [],
      skills: profile.skills || [],
      education: profile.education || [],
      preferences: {
        communicationStyle: profile.preferences?.communicationStyle || 'balanced',
        feedbackPreference: profile.preferences?.feedbackPreference || 'constructive',
        pacePreference: profile.preferences?.pacePreference || 'normal',
      },
      resumeData: {
        personalInfo: profile.personalInfo,
        workExperience: profile.workExperience,
        skills: profile.skills?.map(skill => skill.name) || [],
        education: profile.education,
      },
      metadata: {
        createdAt: currentProfile?.metadata?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        source: 'manual_input',
        version: 1,
      },
      // 必須フィールドを追加
      createdAt: currentProfile?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_agent', // 実際の実装では認証情報から取得
      dataSource: 'manual',
    };

    onProfileSet(completeProfile);
    toast({
      title: 'プロファイル保存完了',
      description: '候補者プロファイルが設定されました',
      status: 'success',
      duration: 3000,
    });
  };

  const handleClearProfile = () => {
    setProfile({
      personalInfo: { name: '', email: '', currentPosition: '', yearsOfExperience: 0 },
      workExperience: [],
      skills: [],
      education: [],
      preferences: {
        communicationStyle: 'balanced',
        feedbackPreference: 'constructive',
        pacePreference: 'normal',
      },
    });
    onProfileSet(null);
    toast({
      title: 'プロファイルクリア',
      description: '候補者プロファイルがクリアされました',
      status: 'info',
      duration: 2000,
    });
  };

  if (simplified) {
    // 簡易版フォーム
    return (
      <VStack align="stretch" spacing={4}>
        <SimpleGrid columns={2} spacing={4}>
          <FormControl isRequired>
            <FormLabel fontSize="sm">候補者名</FormLabel>
            <Input
              value={profile.personalInfo?.name || ''}
              onChange={(e) => handlePersonalInfoChange('name', e.target.value)}
              placeholder="山田 太郎"
              size="sm"
            />
          </FormControl>
          
          <FormControl>
            <FormLabel fontSize="sm">現在の職位</FormLabel>
            <Input
              value={profile.personalInfo?.currentPosition || ''}
              onChange={(e) => handlePersonalInfoChange('currentPosition', e.target.value)}
              placeholder="フロントエンドエンジニア"
              size="sm"
            />
          </FormControl>
        </SimpleGrid>

        <FormControl>
          <FormLabel fontSize="sm">経験年数</FormLabel>
          <NumberInput
            value={profile.personalInfo?.yearsOfExperience || ''}
            onChange={(value) => handlePersonalInfoChange('yearsOfExperience', parseInt(value) || 0)}
            min={0}
            max={50}
            size="sm"
          >
            <NumberInputField placeholder="5" />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
        </FormControl>

        {/* スキル入力 */}
        <FormControl>
          <FormLabel fontSize="sm">主要スキル</FormLabel>
          <VStack align="stretch" spacing={2}>
            <HStack>
              <Input
                value={tempSkill}
                onChange={(e) => setTempSkill(e.target.value)}
                placeholder="React, TypeScript, etc."
                size="sm"
                onKeyPress={(e) => e.key === 'Enter' && addSkill()}
              />
              <IconButton
                aria-label="スキル追加"
                icon={<AddIcon />}
                onClick={addSkill}
                size="sm"
                colorScheme="blue"
              />
            </HStack>
            {profile.skills && profile.skills.length > 0 && (
              <HStack spacing={2} flexWrap="wrap">
                {profile.skills.map((skill, index) => (
                  <Badge
                    key={index}
                    colorScheme="blue"
                    variant="solid"
                    display="flex"
                    alignItems="center"
                    gap={1}
                  >
                    {skill.name}
                    <Box
                      as="button"
                      onClick={() => removeSkill(index)}
                      ml={1}
                      fontSize="xs"
                    >
                      ×
                    </Box>
                  </Badge>
                ))}
              </HStack>
            )}
          </VStack>
        </FormControl>

        <HStack spacing={2} pt={2}>
          <Button
            leftIcon={<CheckIcon />}
            colorScheme="blue"
            onClick={handleSaveProfile}
            size="sm"
            flex={1}
          >
            プロファイル設定
          </Button>
          <Button
            variant="outline"
            onClick={handleClearProfile}
            size="sm"
          >
            クリア
          </Button>
        </HStack>

        {currentProfile && (
          <Box p={3} bg="green.50" borderRadius="md" borderWidth="1px" borderColor="green.200">
            <Text fontSize="sm" color="green.700" fontWeight="bold">
              ✓ プロファイル設定済み: {currentProfile.personalInfo.name}
            </Text>
            {currentProfile.personalInfo.currentPosition && (
              <Text fontSize="xs" color="green.600">
                {currentProfile.personalInfo.currentPosition}
                {currentProfile.personalInfo.yearsOfExperience && 
                  ` (${currentProfile.personalInfo.yearsOfExperience}年)`
                }
              </Text>
            )}
          </Box>
        )}
      </VStack>
    );
  }

  // 詳細版フォーム（将来の拡張用）
  return (
    <VStack align="stretch" spacing={6}>
      <Accordion allowToggle>
        <AccordionItem>
          <AccordionButton>
            <Box flex="1" textAlign="left">
              <Text fontWeight="bold">基本情報</Text>
            </Box>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel>
            <VStack align="stretch" spacing={4}>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired>
                  <FormLabel>候補者名</FormLabel>
                  <Input
                    value={profile.personalInfo?.name || ''}
                    onChange={(e) => handlePersonalInfoChange('name', e.target.value)}
                    placeholder="山田 太郎"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel>メールアドレス</FormLabel>
                  <Input
                    type="email"
                    value={profile.personalInfo?.email || ''}
                    onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </FormControl>
              </SimpleGrid>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl>
                  <FormLabel>現職・職位</FormLabel>
                  <Input
                    value={profile.personalInfo?.currentPosition || ''}
                    onChange={(e) => handlePersonalInfoChange('currentPosition', e.target.value)}
                    placeholder="フロントエンドエンジニア"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel>経験年数</FormLabel>
                  <NumberInput
                    value={profile.personalInfo?.yearsOfExperience || ''}
                    onChange={(value) => handlePersonalInfoChange('yearsOfExperience', parseInt(value) || 0)}
                    min={0}
                    max={50}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>
              </SimpleGrid>
            </VStack>
          </AccordionPanel>
        </AccordionItem>

        {/* 他のセクションは省略（将来の拡張用） */}
      </Accordion>

      <HStack spacing={4}>
        <Button
          leftIcon={<CheckIcon />}
          colorScheme="blue"
          onClick={handleSaveProfile}
          flex={1}
        >
          プロファイル保存
        </Button>
        <Button variant="outline" onClick={handleClearProfile}>
          クリア
        </Button>
      </HStack>
    </VStack>
  );
}

export default CandidateProfileInput;