/**
 * 新規候補者登録モーダル
 */

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Switch,
  useToast
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import CandidateProfileInput from '../../CandidateProfileInput';

interface NewCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  isEditing?: boolean;
  candidateName: string;
  candidateNameKana: string;
  candidateEmail: string;
  candidateAge: number;
  candidateStatus: string;
  candidateCompany: string;
  candidatePosition: string;
  candidateAddress: string;
  candidateHasResume: boolean;
  candidateHasCareerHistory: boolean;
  candidateProfile: any;
  onCandidateNameChange: (name: string) => void;
  onCandidateNameKanaChange: (nameKana: string) => void;
  onCandidateEmailChange: (email: string) => void;
  onCandidateAgeChange: (age: number) => void;
  onCandidateStatusChange: (status: string) => void;
  onCandidateCompanyChange: (company: string) => void;
  onCandidatePositionChange: (position: string) => void;
  onCandidateAddressChange: (address: string) => void;
  onCandidateHasResumeChange: (hasResume: boolean) => void;
  onCandidateHasCareerHistoryChange: (hasCareerHistory: boolean) => void;
  onCandidateProfileChange: (profile: any) => void;
  onRegister: () => void;
}

export const NewCandidateModal: React.FC<NewCandidateModalProps> = ({
  isOpen,
  onClose,
  isEditing = false,
  candidateName,
  candidateNameKana,
  candidateEmail,
  candidateAge,
  candidateStatus,
  candidateCompany,
  candidatePosition,
  candidateAddress,
  candidateHasResume,
  candidateHasCareerHistory,
  candidateProfile,
  onCandidateNameChange,
  onCandidateNameKanaChange,
  onCandidateEmailChange,
  onCandidateAgeChange,
  onCandidateStatusChange,
  onCandidateCompanyChange,
  onCandidatePositionChange,
  onCandidateAddressChange,
  onCandidateHasResumeChange,
  onCandidateHasCareerHistoryChange,
  onCandidateProfileChange,
  onRegister
}) => {
  const toast = useToast();

  const isFormValid = candidateName.trim() && 
                     candidateNameKana.trim() && 
                     candidateEmail.trim() && 
                     candidateAge > 0 && 
                     candidateStatus && 
                     candidateCompany.trim() && 
                     candidatePosition.trim() &&
                     candidateAddress.trim();

  const handleRegister = () => {
    if (!isFormValid) {
      toast({
        title: '入力エラー',
        description: '必須項目をすべて入力してください',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    onRegister();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: "full", sm: "xl", md: "2xl" }}>
      <ModalOverlay />
      <ModalContent 
        maxH={{ base: "calc(100vh - 32px)", sm: "calc(100vh - 64px)", md: "calc(100vh - 96px)" }}
        overflowY="auto"
        mx={{ base: 4, sm: 8, md: 12 }}
        my={{ base: 4, sm: 8, md: 12 }}
        maxW={{ base: "calc(100vw - 32px)", sm: "calc(100vw - 64px)", md: "calc(100vw - 96px)" }}
      >
        <ModalHeader>
          <HStack>
            <AddIcon color={isEditing ? "blue.500" : "green.500"} />
            <Text>{isEditing ? "候補者情報編集" : "新規候補者登録"}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Alert status="info" size="sm">
              <AlertIcon />
              <AlertDescription fontSize="xs">
                候補者の詳細情報を入力してください。すべての情報が面接カスタマイズに活用されます。
              </AlertDescription>
            </Alert>

            {/* 基本情報 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">👤 基本情報</Text>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">氏名</FormLabel>
                  <Input
                    value={candidateName}
                    onChange={(e) => onCandidateNameChange(e.target.value)}
                    placeholder="例：山田 太郎"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">氏名（かな）</FormLabel>
                  <Input
                    value={candidateNameKana}
                    onChange={(e) => onCandidateNameKanaChange(e.target.value)}
                    placeholder="例：やまだ たろう"
                    size="sm"
                  />
                </FormControl>
              </SimpleGrid>

              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">メールアドレス</FormLabel>
                  <Input
                    type="email"
                    value={candidateEmail}
                    onChange={(e) => onCandidateEmailChange(e.target.value)}
                    placeholder="例：<EMAIL>"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">年齢</FormLabel>
                  <Input
                    type="number"
                    value={candidateAge}
                    onChange={(e) => onCandidateAgeChange(parseInt(e.target.value) || 0)}
                    placeholder="例：32"
                    size="sm"
                    min="18"
                    max="70"
                  />
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* 就労状況 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">💼 就労状況</Text>
              
              <FormControl isRequired>
                <FormLabel fontSize="sm">現在の就労状況</FormLabel>
                <Select 
                  placeholder="選択してください" 
                  size="sm"
                  value={candidateStatus}
                  onChange={(e) => onCandidateStatusChange(e.target.value)}
                >
                  <option value="employed">現職</option>
                  <option value="unemployed">離職中</option>
                </Select>
              </FormControl>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">
                    {candidateStatus === 'employed' ? '現在の勤務先' : '前職の勤務先'}
                  </FormLabel>
                  <Input
                    value={candidateCompany}
                    onChange={(e) => onCandidateCompanyChange(e.target.value)}
                    placeholder="例：株式会社ABC"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">職種</FormLabel>
                  <Input
                    value={candidatePosition}
                    onChange={(e) => onCandidatePositionChange(e.target.value)}
                    placeholder="例：フロントエンドエンジニア"
                    size="sm"
                  />
                </FormControl>
              </SimpleGrid>
              
              <FormControl isRequired>
                <FormLabel fontSize="sm">住所（都道府県・市区町村）</FormLabel>
                <Input
                  value={candidateAddress}
                  onChange={(e) => onCandidateAddressChange(e.target.value)}
                  placeholder="例：東京都渋谷区"
                  size="sm"
                />
              </FormControl>
            </VStack>

            {/* 応募書類 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">📄 応募書類</Text>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl>
                  <FormLabel fontSize="sm">履歴書</FormLabel>
                  <HStack>
                    <Switch
                      isChecked={candidateHasResume}
                      onChange={(e) => onCandidateHasResumeChange(e.target.checked)}
                      colorScheme="green"
                    />
                    <Text fontSize="sm">アップロード済み</Text>
                  </HStack>
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">職務経歴書</FormLabel>
                  <HStack>
                    <Switch
                      isChecked={candidateHasCareerHistory}
                      onChange={(e) => onCandidateHasCareerHistoryChange(e.target.checked)}
                      colorScheme="green"
                    />
                    <Text fontSize="sm">アップロード済み</Text>
                  </HStack>
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* プロファイル作成 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">🎯 詳細プロファイル（任意）</Text>
              <Alert status="success" size="sm">
                <AlertIcon />
                <AlertDescription fontSize="xs">
                  詳細な候補者プロファイルを作成して面接質問をカスタマイズ
                </AlertDescription>
              </Alert>

              <CandidateProfileInput
                currentProfile={candidateProfile}
                onProfileSet={onCandidateProfileChange}
              />
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            キャンセル
          </Button>
          <Button
            colorScheme={isEditing ? "blue" : "green"}
            onClick={handleRegister}
            isDisabled={!isFormValid}
          >
            {isEditing ? "更新" : "登録"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};