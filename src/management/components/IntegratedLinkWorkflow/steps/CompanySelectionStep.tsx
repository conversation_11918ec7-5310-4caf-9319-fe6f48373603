/**
 * ステップ2: 企業選択・検索
 */

import React, { useState, useEffect } from 'react';
import {
  VStack,
  HStack,
  Text,
  Button,
  Input,
  Card,
  CardBody,
  Badge,
  IconButton,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Box,
  InputGroup,
  InputLeftElement,
  Spinner,
  useToast,
  List,
  ListItem
} from '@chakra-ui/react';
import {
  SearchIcon,
  CheckIcon,
  AddIcon,
  ArrowBackIcon,
  ArrowForwardIcon
} from '@chakra-ui/icons';
import { CompanyInfo } from '../types';
import { CompanyMaster, CompanySuggestion, PositionMaster, PositionSuggestion } from '@mensetsu-kun/shared/types/company-master';
import { CompanyMasterService } from '@mensetsu-kun/shared/services/companyMasterService';

interface CompanySelectionStepProps {
  selectedCompany: CompanyInfo | null;
  selectedPosition: PositionMaster | null;
  onCompanySelect: (company: CompanyInfo) => void;
  onPositionSelect: (position: PositionMaster | null) => void;
  onPrevStep: () => void;
  onNextStep: () => void;
}

// CompanyMasterをCompanyInfoに変換するヘルパー関数
const convertCompanyMasterToInfo = (master: CompanyMaster): CompanyInfo => ({
  id: master.id,
  name: master.name,
  industry: master.industry,
  size: master.size === 'startup' ? '小規模（10-50名）' : 
        master.size === 'sme' ? '中規模（50-300名）' : 
        master.size === 'large' ? '大規模（300-1000名）' : '超大規模（1000名以上）',
  description: master.description,
  website: master.website,
  address: master.location?.[0] || '',
  foundedYear: 2020, // デフォルト値（マスターデータに含まれていない場合）
  businessType: 'BtoB', // デフォルト値
  tags: master.values || []
});

// レベルの日本語変換
const getLevelLabel = (level: string): string => {
  switch (level) {
    case 'entry': return '新卒・エントリー';
    case 'mid': return '中堅手';
    case 'senior': return 'シニア';
    case 'lead': return 'リード';
    case 'manager': return 'マネージャー';
    default: return level;
  }
};

export const CompanySelectionStep: React.FC<CompanySelectionStepProps> = ({
  selectedCompany,
  selectedPosition,
  onCompanySelect,
  onPositionSelect,
  onPrevStep,
  onNextStep
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [positionQuery, setPositionQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isPositionSearching, setIsPositionSearching] = useState(false);
  const [suggestions, setSuggestions] = useState<CompanySuggestion[]>([]);
  const [positionSuggestions, setPositionSuggestions] = useState<PositionSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showPositionSuggestions, setShowPositionSuggestions] = useState(false);
  const [allCompanies, setAllCompanies] = useState<CompanyMaster[]>([]);
  const toast = useToast();
  
  // CompanyMasterServiceのインスタンス化を安全に行う
  const [companyService] = useState(() => {
    try {
      return new CompanyMasterService();
    } catch (error) {
      console.error('CompanyMasterService初期化エラー:', error);
      return null;
    }
  });

  // 初期化時に全企業を取得
  useEffect(() => {
    if (companyService) {
      try {
        const companies = companyService.getAllCompanies();
        setAllCompanies(companies);
      } catch (error) {
        console.error('企業一覧取得エラー:', error);
        setAllCompanies([]);
      }
    }
  }, [companyService]);

  // 検索クエリ変更時にサジェッション取得
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (searchQuery.trim().length > 0 && companyService) {
        setIsSearching(true);
        try {
          const suggestionResponse = await companyService.suggestCompanies(searchQuery);
          // レスポンスは直接CompanySuggestion[]配列
          if (suggestionResponse && Array.isArray(suggestionResponse)) {
            setSuggestions(suggestionResponse);
            setShowSuggestions(true);
          } else {
            console.warn('サジェッションレスポンスの形式が期待と異なります:', suggestionResponse);
            setSuggestions([]);
            setShowSuggestions(false);
          }
        } catch (error) {
          console.error('企業サジェッション取得エラー:', error);
          setSuggestions([]);
          setShowSuggestions(false);
          toast({
            title: 'サジェッション取得エラー',
            description: '企業の候補取得中にエラーが発生しました',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        } finally {
          setIsSearching(false);
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    };

    const debounceTimer = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // 職種検索クエリ変更時にサジェッション取得
  useEffect(() => {
    const fetchPositionSuggestions = async () => {
      if (positionQuery.trim().length > 0 && companyService && selectedCompany) {
        setIsPositionSearching(true);
        try {
          const suggestionResponse = await companyService.suggestPositions(
            positionQuery, 
            selectedCompany.id
          );
          if (suggestionResponse && Array.isArray(suggestionResponse)) {
            setPositionSuggestions(suggestionResponse);
            setShowPositionSuggestions(true);
          } else {
            setPositionSuggestions([]);
            setShowPositionSuggestions(false);
          }
        } catch (error) {
          console.error('職種サジェッション取得エラー:', error);
          setPositionSuggestions([]);
          setShowPositionSuggestions(false);
        } finally {
          setIsPositionSearching(false);
        }
      } else {
        setPositionSuggestions([]);
        setShowPositionSuggestions(false);
      }
    };

    const debounceTimer = setTimeout(fetchPositionSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [positionQuery, selectedCompany]);

  // 企業選択処理
  const handleCompanySelect = (company: CompanyMaster) => {
    const convertedCompany = convertCompanyMasterToInfo(company);
    onCompanySelect(convertedCompany);
    // 企業変更時は職種をリセット
    onPositionSelect(null);
    setPositionQuery('');
    setSearchQuery('');
    setShowSuggestions(false);
    toast({
      title: '企業選択完了',
      description: `${company.name}を選択しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // サジェッションから企業選択
  const handleSuggestionSelect = async (suggestion: CompanySuggestion) => {
    if (!companyService) {
      toast({
        title: 'エラー',
        description: '企業サービスが利用できません',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    try {
      const company = await companyService.getCompanyById(suggestion.id);
      if (company) {
        handleCompanySelect(company);
      } else {
        toast({
          title: 'エラー',
          description: '選択された企業情報が見つかりません',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('企業情報取得エラー:', error);
      toast({
        title: 'エラー',
        description: '企業情報の取得に失敗しました',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // 職種サジェッションから選択
  const handlePositionSuggestionSelect = async (suggestion: PositionSuggestion) => {
    if (!companyService) return;
    
    try {
      const company = await companyService.getCompanyById(suggestion.companyId);
      if (company) {
        const position = company.positions.find(p => p.id === suggestion.id);
        if (position) {
          onPositionSelect(position);
          setPositionQuery('');
          setShowPositionSuggestions(false);
          toast({
            title: '職種選択完了',
            description: `${position.title}を選択しました`,
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error('職種情報取得エラー:', error);
    }
  };

  // 直接職種選択
  const handleDirectPositionSelect = (position: PositionMaster) => {
    onPositionSelect(position);
    toast({
      title: '職種選択完了',
      description: `${position.title}を選択しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <VStack spacing={6} align="stretch">
      <Alert status="info">
        <AlertIcon />
        <AlertDescription fontSize="sm">
          面接対象の企業を検索・選択してください。企業情報は質問生成の精度向上に使用されます。
        </AlertDescription>
      </Alert>

      {/* 企業検索 */}
      <Card variant="outline">
        <CardBody>
          <VStack spacing={4} align="stretch">
            <Text fontWeight="bold" fontSize="md">🔍 企業検索</Text>
            
            <Box position="relative">
              <InputGroup>
                <InputLeftElement pointerEvents="none">
                  <SearchIcon color="gray.300" />
                </InputLeftElement>
                <Input
                  placeholder="企業名、業界、キーワードで検索..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => searchQuery && setShowSuggestions(true)}
                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                />
                {isSearching && (
                  <Box position="absolute" right={3} top={3}>
                    <Spinner size="sm" color="blue.500" />
                  </Box>
                )}
              </InputGroup>
              
              {/* サジェッション一覧 */}
              {showSuggestions && suggestions && Array.isArray(suggestions) && suggestions.length > 0 && (
                <Card 
                  position="absolute" 
                  top="100%" 
                  left={0} 
                  right={0} 
                  zIndex={10}
                  mt={1}
                  variant="outline"
                  bg="white"
                  boxShadow="lg"
                >
                  <CardBody p={0}>
                    <List>
                      {(suggestions || []).map((suggestion, index) => (
                        <ListItem
                          key={suggestion.id}
                          p={3}
                          cursor="pointer"
                          _hover={{ bg: 'blue.50' }}
                          borderBottom={index < suggestions.length - 1 ? '1px' : 'none'}
                          borderColor="gray.100"
                          onClick={() => handleSuggestionSelect(suggestion)}
                        >
                          <VStack align="start" spacing={1}>
                            <HStack justify="space-between" w="100%">
                              <Text fontWeight="medium" fontSize="sm">
                                {suggestion.name}
                              </Text>
                              <Badge colorScheme="blue" size="sm">
                                {Math.round(suggestion.matchScore * 100)}%
                              </Badge>
                            </HStack>
                            <Text fontSize="xs" color="gray.600">
                              {suggestion.industry}
                            </Text>
                            {suggestion.usageCount > 0 && (
                              <Text fontSize="xs" color="gray.500">
                                使用回数: {suggestion.usageCount}回
                              </Text>
                            )}
                          </VStack>
                        </ListItem>
                      ))}
                    </List>
                  </CardBody>
                </Card>
              )}
            </Box>
            
            <Text fontSize="sm" color="gray.600">
              💡 ヒント: 企業名の一部や業界名（例：「IT」「コンサル」）でも検索できます
            </Text>
          </VStack>
        </CardBody>
      </Card>

      {/* 選択された企業の表示 */}
      {selectedCompany && (
        <Card bg="blue.50" variant="outline" borderColor="blue.300">
          <CardBody>
            <VStack align="stretch" spacing={3}>
              <HStack justify="space-between">
                <Text fontWeight="bold" fontSize="md">✅ 選択中の企業</Text>
                <Badge colorScheme="blue" size="lg">選択済み</Badge>
              </HStack>
              
              <Card variant="outline" bg="white">
                <CardBody>
                  <VStack align="start" spacing={2}>
                    <HStack justify="space-between" w="100%">
                      <Text fontWeight="bold" fontSize="lg">{selectedCompany.name}</Text>
                      <Badge colorScheme="green">{selectedCompany.industry}</Badge>
                    </HStack>
                    
                    <Text fontSize="sm" color="gray.600">{selectedCompany.description}</Text>
                    
                    <HStack spacing={4} fontSize="sm" color="gray.600">
                      <Text><strong>規模:</strong> {selectedCompany.size}</Text>
                      <Text><strong>設立:</strong> {selectedCompany.foundedYear}年</Text>
                      <Text><strong>事業:</strong> {selectedCompany.businessType}</Text>
                    </HStack>
                    
                    <HStack spacing={2} flexWrap="wrap">
                      {selectedCompany.tags.map((tag, index) => (
                        <Badge key={index} colorScheme="gray" size="sm">{tag}</Badge>
                      ))}
                    </HStack>
                  </VStack>
                </CardBody>
              </Card>
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* 全企業一覧 */}
      <VStack align="stretch" spacing={3}>
        <Text fontWeight="bold" fontSize="md">
          🏢 企業一覧 ({allCompanies.length}件)
        </Text>
        
        {allCompanies.length > 0 ? (
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            {allCompanies.map((company) => (
              <Card 
                key={company.id}
                variant="outline"
                cursor="pointer"
                _hover={{ borderColor: 'blue.400', bg: 'blue.50' }}
                borderColor={selectedCompany?.id === company.id ? 'blue.400' : 'gray.200'}
                bg={selectedCompany?.id === company.id ? 'blue.50' : 'white'}
                onClick={() => handleCompanySelect(company)}
              >
                <CardBody>
                  <VStack align="start" spacing={3}>
                    <HStack justify="space-between" w="100%">
                      <Text fontWeight="bold" fontSize="md">{company.name}</Text>
                      <Badge colorScheme="blue">{company.industry}</Badge>
                    </HStack>
                    
                    <Text fontSize="sm" color="gray.600" noOfLines={2}>
                      {company.description}
                    </Text>
                    
                    <HStack spacing={4} fontSize="xs" color="gray.500">
                      <Text>{company.size === 'startup' ? '小規模（10-50名）' : 
                             company.size === 'sme' ? '中規模（50-300名）' : 
                             company.size === 'large' ? '大規模（300-1000名）' : '超大規模（1000名以上）'}</Text>
                      <Text>{company.interviewStyle}面接</Text>
                    </HStack>
                    
                    <HStack spacing={2} flexWrap="wrap">
                      {(company.values || []).slice(0, 3).map((tag, index) => (
                        <Badge key={index} colorScheme="gray" size="sm">{tag}</Badge>
                      ))}
                      {(company.values || []).length > 3 && (
                        <Badge colorScheme="gray" size="sm">+{(company.values || []).length - 3}</Badge>
                      )}
                    </HStack>
                    
                    <Button
                      size="sm"
                      colorScheme="blue"
                      variant={selectedCompany?.id === company.id ? "solid" : "outline"}
                      rightIcon={selectedCompany?.id === company.id ? <CheckIcon /> : <AddIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCompanySelect(company);
                      }}
                      w="100%"
                    >
                      {selectedCompany?.id === company.id ? '選択済み' : 'この企業を選択'}
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        ) : (
          <Alert status="info">
            <AlertIcon />
            <AlertDescription>
              企業データを読み込み中です。しばらくお待ちください。
            </AlertDescription>
          </Alert>
        )}
      </VStack>

      {/* 職種選択セクション */}
      {selectedCompany && (
        <Card bg="green.50" variant="outline" borderColor="green.300">
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontWeight="bold" fontSize="md">💼 職種選択</Text>
                <Badge colorScheme="green" size="lg">任意</Badge>
              </HStack>
              
              {/* 職種検索 */}
              <Box position="relative">
                <InputGroup>
                  <InputLeftElement pointerEvents="none">
                    <SearchIcon color="gray.300" />
                  </InputLeftElement>
                  <Input
                    placeholder="職種名で検索..."
                    value={positionQuery}
                    onChange={(e) => setPositionQuery(e.target.value)}
                    onFocus={() => positionQuery && setShowPositionSuggestions(true)}
                    onBlur={() => setTimeout(() => setShowPositionSuggestions(false), 200)}
                  />
                  {isPositionSearching && (
                    <Box position="absolute" right={3} top={3}>
                      <Spinner size="sm" color="green.500" />
                    </Box>
                  )}
                </InputGroup>
                
                {/* 職種サジェッション */}
                {showPositionSuggestions && positionSuggestions && Array.isArray(positionSuggestions) && positionSuggestions.length > 0 && (
                  <Card 
                    position="absolute" 
                    top="100%" 
                    left={0} 
                    right={0} 
                    zIndex={10}
                    mt={1}
                    variant="outline"
                    bg="white"
                    boxShadow="lg"
                  >
                    <CardBody p={0}>
                      <List>
                        {(positionSuggestions || []).map((suggestion, index) => (
                          <ListItem
                            key={suggestion.id}
                            p={3}
                            cursor="pointer"
                            _hover={{ bg: 'green.50' }}
                            borderBottom={index < positionSuggestions.length - 1 ? '1px' : 'none'}
                            borderColor="gray.100"
                            onClick={() => handlePositionSuggestionSelect(suggestion)}
                          >
                            <VStack align="start" spacing={1}>
                              <HStack justify="space-between" w="100%">
                                <Text fontWeight="medium" fontSize="sm">
                                  {suggestion.title}
                                </Text>
                                <Badge colorScheme="green" size="sm">
                                  {Math.round(suggestion.matchScore * 100)}%
                                </Badge>
                              </HStack>
                              <Text fontSize="xs" color="gray.600">
                                {suggestion.department} • {getLevelLabel(suggestion.level)}
                              </Text>
                            </VStack>
                          </ListItem>
                        ))}
                      </List>
                    </CardBody>
                  </Card>
                )}
              </Box>
              
              {/* 選択された職種の表示 */}
              {selectedPosition && (
                <Card variant="outline" bg="white">
                  <CardBody>
                    <VStack align="start" spacing={2}>
                      <HStack justify="space-between" w="100%">
                        <Text fontWeight="bold" fontSize="md">{selectedPosition.title}</Text>
                        <Badge colorScheme="green">選択済み</Badge>
                      </HStack>
                      <HStack spacing={4} fontSize="sm" color="gray.600">
                        <Text>{selectedPosition.department}</Text>
                        <Text>{getLevelLabel(selectedPosition.level)}</Text>
                        <Text>予想時間: {selectedPosition.expectedDuration}分</Text>
                      </HStack>
                      {selectedPosition.requirements && (
                        <Box>
                          <Text fontSize="xs" color="gray.500" fontWeight="bold">必須スキル:</Text>
                          <HStack spacing={2} flexWrap="wrap">
                            {selectedPosition.requirements.slice(0, 2).map((req, index) => (
                              <Badge key={index} colorScheme="orange" size="sm">{req}</Badge>
                            ))}
                          </HStack>
                        </Box>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              )}
              
              {/* 利用可能職種一覧 */}
              {allCompanies.find(c => c.id === selectedCompany.id)?.positions && allCompanies.find(c => c.id === selectedCompany.id)!.positions.length > 0 && (
                <VStack align="stretch" spacing={2}>
                  <Text fontWeight="medium" fontSize="sm">利用可能職種:</Text>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={2}>
                    {allCompanies.find(c => c.id === selectedCompany.id)!.positions.map((position) => (
                      <Card 
                        key={position.id}
                        variant="outline"
                        cursor="pointer"
                        size="sm"
                        _hover={{ borderColor: 'green.400', bg: 'green.50' }}
                        borderColor={selectedPosition?.id === position.id ? 'green.400' : 'gray.200'}
                        bg={selectedPosition?.id === position.id ? 'green.50' : 'white'}
                        onClick={() => handleDirectPositionSelect(position)}
                      >
                        <CardBody p={3}>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium" fontSize="sm">{position.title}</Text>
                            <HStack spacing={2} fontSize="xs" color="gray.600">
                              <Text>{position.department}</Text>
                              <Text>{getLevelLabel(position.level)}</Text>
                            </HStack>
                            <Badge 
                              colorScheme={selectedPosition?.id === position.id ? 'green' : 'gray'} 
                              size="sm"
                            >
                              {selectedPosition?.id === position.id ? '選択済み' : '選択する'}
                            </Badge>
                          </VStack>
                        </CardBody>
                      </Card>
                    ))}
                  </SimpleGrid>
                </VStack>
              )}
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* アクションボタン */}
      <HStack justify="space-between">
        <Button
          leftIcon={<ArrowBackIcon />}
          variant="outline"
          onClick={onPrevStep}
        >
          戻る
        </Button>
        
        <Button
          rightIcon={<ArrowForwardIcon />}
          colorScheme="blue"
          onClick={onNextStep}
          isDisabled={!selectedCompany}
          size="lg"
        >
          企業資料アップロードへ
        </Button>
      </HStack>
    </VStack>
  );
};