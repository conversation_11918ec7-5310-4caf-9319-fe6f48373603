/**
 * ステップ1: 企業資料アップロード
 */

import React, { useRef } from 'react';
import {
  VStack,
  HStack,
  Text,
  Button,
  Box,
  Card,
  CardBody,
  CardHeader,
  Input,
  Switch,
  IconButton,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,
  AlertDescription
} from '@chakra-ui/react';
import {
  AttachmentIcon,
  DeleteIcon,
  CheckCircleIcon,
} from '@chakra-ui/icons';
import { FiUsers } from 'react-icons/fi';
import { CompanyDocument } from '../types';
import { FILE_UPLOAD_SETTINGS } from '../constants/steps';

interface DocumentUploadStepProps {
  uploadedDocs: CompanyDocument[];
  enhancedMode: boolean;
  isLoading: boolean;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteDoc: (docId: string) => void;
  onEnhancedModeToggle: (enabled: boolean) => void;
  onPrevStep: () => void;
  onGenerateQuestions: () => void;
  formatFileSize: (bytes: number) => string;
}

export const DocumentUploadStep: React.FC<DocumentUploadStepProps> = ({
  uploadedDocs,
  enhancedMode,
  isLoading,
  onFileUpload,
  onDeleteDoc,
  onEnhancedModeToggle,
  onPrevStep,
  onGenerateQuestions,
  formatFileSize
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const stepsBg = useColorModeValue('blue.50', 'blue.900');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <VStack spacing={4} align="stretch">
      {/* 高機能モード切り替え */}
      <Card bg={stepsBg} p={4}>
        <VStack spacing={3} align="stretch">
          <HStack justify="space-between" align="center">
            <VStack align="start" spacing={1}>
              <Text fontWeight="bold" fontSize="sm">
                🧠 インテリジェント質問生成モード
              </Text>
              <Text fontSize="xs" color="gray.600">
                プロダクト憲法に基づく心理的安全性を重視した高度な質問生成
              </Text>
            </VStack>
            <Switch
              isChecked={enhancedMode}
              onChange={(e) => onEnhancedModeToggle(e.target.checked)}
              colorScheme="blue"
              size="lg"
            />
          </HStack>

          {enhancedMode && (
            <Alert status="success" size="sm">
              <AlertIcon />
              <AlertDescription fontSize="xs">
                高度なAI分析が有効です。候補者の心理的安全性を考慮した質問を生成します。
              </AlertDescription>
            </Alert>
          )}
        </VStack>
      </Card>

      {/* ファイルアップロード領域 */}
      <Box 
        borderWidth={2} 
        borderStyle="dashed" 
        borderColor={borderColor} 
        borderRadius="lg" 
        p={6} 
        textAlign="center"
        bg={useColorModeValue('gray.50', 'gray.800')}
        _hover={{ borderColor: 'blue.400', bg: useColorModeValue('blue.50', 'blue.900') }}
        transition="all 0.2s"
      >
        <Input
          type="file"
          multiple={FILE_UPLOAD_SETTINGS.MULTIPLE}
          accept={FILE_UPLOAD_SETTINGS.ACCEPT_TYPES}
          onChange={onFileUpload}
          style={{ display: 'none' }}
          ref={fileInputRef}
        />
        <VStack spacing={3}>
          <AttachmentIcon w={8} h={8} color="gray.400" />
          <Text fontSize="lg" fontWeight="bold">企業資料をアップロード</Text>
          <Text fontSize="sm" color="gray.600">
            PDF、Word、テキストファイルに対応しています（複数選択可）
          </Text>
          <Button 
            colorScheme="blue" 
            onClick={() => fileInputRef.current?.click()}
            size="lg"
          >
            ファイルを選択
          </Button>
          <Text fontSize="xs" color="gray.500">
            最大ファイルサイズ: {formatFileSize(FILE_UPLOAD_SETTINGS.MAX_SIZE)}
          </Text>
        </VStack>
      </Box>

      {/* アップロード済みファイル一覧 */}
      {uploadedDocs.length > 0 && (
        <VStack align="stretch" spacing={3}>
          <Text fontWeight="bold">📄 アップロード済みファイル ({uploadedDocs.length}件)</Text>
          {uploadedDocs.map((doc) => (
            <Card key={doc.id} variant="outline">
              <CardBody py={3}>
                <HStack justify="space-between">
                  <HStack spacing={3}>
                    <AttachmentIcon color="blue.500" />
                    <VStack align="start" spacing={0}>
                      <Text fontSize="sm" fontWeight="medium">{doc.fileName}</Text>
                      <Text fontSize="xs" color="gray.600">
                        {formatFileSize(doc.fileSize)} • {new Date(doc.uploadedAt).toLocaleString()}
                      </Text>
                    </VStack>
                  </HStack>
                  <IconButton
                    icon={<DeleteIcon />}
                    size="sm"
                    variant="ghost"
                    colorScheme="red"
                    aria-label="ファイルを削除"
                    onClick={() => onDeleteDoc(doc.id)}
                  />
                </HStack>
              </CardBody>
            </Card>
          ))}
        </VStack>
      )}

      {/* 進行状況とヒント */}
      <Alert status="info">
        <AlertIcon />
        <AlertDescription fontSize="sm">
          {uploadedDocs.length > 0 
            ? `${uploadedDocs.length}件のファイルがアップロードされました。次のステップでAIが企業情報を分析し、質問意図を生成します。`
            : `ファイルのアップロードは任意です。アップロードすると、より企業に特化した質問を生成できます。`
          }
        </AlertDescription>
      </Alert>

      {/* アクションボタン */}
      <HStack justify="space-between">
        <Button
          leftIcon={<Icon as={FiUsers} />}
          variant="outline"
          onClick={onPrevStep}
        >
          戻る
        </Button>
        
        <Button
          rightIcon={<CheckCircleIcon />}
          colorScheme="blue"
          onClick={onGenerateQuestions}
          isLoading={isLoading}
          loadingText="AI分析中..."
          size="lg"
        >
          AI質問意図生成
        </Button>
      </HStack>
    </VStack>
  );
};