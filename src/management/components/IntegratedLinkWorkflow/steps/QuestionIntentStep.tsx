/**
 * ステップ3: AI質問意図生成・編集
 * テンプレート選択・手動追加・追加生成機能付き
 */

import React, { useState } from 'react';
import {
  VStack,
  HStack,
  Text,
  Button,
  Card,
  CardBody,
  Badge,
  IconButton,
  Alert,
  AlertIcon,
  AlertDescription,
  Divider,
  FormControl,
  FormLabel,
  Input,
  Switch,
  Select,
  Box,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Textarea,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  TagCloseButton,
  useToast
} from '@chakra-ui/react';
import {
  EditIcon,
  AttachmentIcon,
  LinkIcon,
  AddIcon,
  RepeatIcon,
  CheckIcon,
  StarIcon,
  DeleteIcon
} from '@chakra-ui/icons';
import { AIGeneratedIntent } from '../types';
import { PRIORITY_COLORS } from '../constants/steps';
import { mockTemplates, IntentTemplate, TEMPLATE_CATEGORIES } from '../constants/templates';
import { useTemplateState } from '../hooks/useTemplateState';

interface QuestionIntentStepProps {
  generatedIntents: AIGeneratedIntent[];
  shouldSaveAsTemplate: boolean;
  templateName: string;
  onEditIntent: (intent: AIGeneratedIntent) => void;
  onSaveAsTemplateToggle: (save: boolean) => void;
  onTemplateNameChange: (name: string) => void;
  onPrevStep: () => void;
  onNextStep: () => void;
  getPriorityColor: (priority: string) => string;
  onIntentsUpdate?: (intents: AIGeneratedIntent[]) => void;
}

export const QuestionIntentStep: React.FC<QuestionIntentStepProps> = ({
  generatedIntents,
  shouldSaveAsTemplate,
  templateName,
  onEditIntent,
  onSaveAsTemplateToggle,
  onTemplateNameChange,
  onPrevStep,
  onNextStep,
  getPriorityColor,
  onIntentsUpdate
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<IntentTemplate | null>(null);
  const [localIntents, setLocalIntents] = useState<AIGeneratedIntent[]>(generatedIntents);
  const [isAddingManual, setIsAddingManual] = useState(false);
  const [manualIntent, setManualIntent] = useState({
    category: '',
    intent: '',
    priority: 'medium' as 'high' | 'medium' | 'low',
    estimatedTime: 5,
    description: ''
  });
  
  const { isOpen: isTemplateModalOpen, onOpen: onTemplateModalOpen, onClose: onTemplateModalClose } = useDisclosure();
  const { isOpen: isManualModalOpen, onOpen: onManualModalOpen, onClose: onManualModalClose } = useDisclosure();
  const toast = useToast();
  const templateState = useTemplateState();
  
  // テンプレートとモックデータを組み合わせて表示
  const allTemplates = [...mockTemplates, ...templateState.templates];

  // テンプレート選択時の処理
  const handleTemplateSelect = (template: IntentTemplate) => {
    setSelectedTemplate(template);
    setLocalIntents(template.intents);
    onIntentsUpdate?.(template.intents);
    onTemplateModalClose();
    
    // 使用回数を増加（モックデータ以外の場合）
    if (!template.id.startsWith('template-')) {
      templateState.incrementUsageCount(template.id);
    }
    
    toast({
      title: 'テンプレート適用完了',
      description: `「${template.name}」を適用しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // 手動追加の処理
  const handleManualAdd = () => {
    const newIntent: AIGeneratedIntent = {
      id: `manual-${Date.now()}`,
      ...manualIntent,
      confidence: 1.0,
      isEdited: true,
      aiInstructions: ''
    };
    
    const updatedIntents = [...localIntents, newIntent];
    setLocalIntents(updatedIntents);
    onIntentsUpdate?.(updatedIntents);
    
    // フォームリセット
    setManualIntent({
      category: '',
      intent: '',
      priority: 'medium',
      estimatedTime: 5,
      description: ''
    });
    onManualModalClose();
    
    toast({
      title: '質問意図を追加しました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // 追加生成の処理
  const handleAdditionalGeneration = () => {
    // 追加生成のシミュレーション
    const additionalIntents: AIGeneratedIntent[] = [
      {
        id: `additional-${Date.now()}`,
        category: '追加質問',
        intent: '現在の志望動機と将来のキャリアビジョンを確認したい',
        description: '候補者の長期的な目標と企業との適合性を評価',
        priority: 'medium',
        estimatedTime: 7,
        confidence: 0.85,
        isEdited: false,
        aiInstructions: '5年後、10年後のビジョンも含めて聞いてください'
      }
    ];
    
    const updatedIntents = [...localIntents, ...additionalIntents];
    setLocalIntents(updatedIntents);
    onIntentsUpdate?.(updatedIntents);
    
    toast({
      title: 'AI追加生成完了',
      description: '新しい質問意図が追加されました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // 意図の削除
  const handleDeleteIntent = (intentId: string) => {
    const updatedIntents = localIntents.filter(intent => intent.id !== intentId);
    setLocalIntents(updatedIntents);
    onIntentsUpdate?.(updatedIntents);
  };

  return (
    <VStack spacing={4} align="stretch">
      {/* タブ切り替え */}
      <Tabs variant="soft-rounded" colorScheme="blue">
        <TabList>
          <Tab>AI生成意図</Tab>
          <Tab>テンプレートから選択</Tab>
        </TabList>

        <TabPanels>
          {/* AI生成タブ */}
          <TabPanel px={0} py={4}>
            <VStack spacing={4} align="stretch">
              <Alert status="info">
                <AlertIcon />
                <AlertDescription fontSize="sm">
                  AIが生成した質問意図を確認・編集してください。必要に応じて手動追加や追加生成も可能です。
                </AlertDescription>
              </Alert>

              {/* アクションボタン群 */}
              <HStack spacing={2}>
                <Button
                  leftIcon={<AddIcon />}
                  size="sm"
                  colorScheme="green"
                  variant="outline"
                  onClick={onManualModalOpen}
                >
                  手動追加
                </Button>
                <Button
                  leftIcon={<RepeatIcon />}
                  size="sm"
                  colorScheme="purple"
                  variant="outline"
                  onClick={handleAdditionalGeneration}
                >
                  追加生成
                </Button>
                <Button
                  leftIcon={<StarIcon />}
                  size="sm"
                  colorScheme="orange"
                  variant="outline"
                  onClick={onTemplateModalOpen}
                >
                  テンプレートから追加
                </Button>
              </HStack>

              {/* 生成された質問意図一覧 */}
              {localIntents.length > 0 ? (
                <VStack spacing={4} align="stretch">
                  <Text fontWeight="bold" fontSize="lg">
                    📋 質問意図一覧 ({localIntents.length}件)
                  </Text>
                  
                  {localIntents.map((intent, index) => (
                    <Card 
                      key={intent.id} 
                      variant="outline" 
                      borderColor={intent.priority === 'high' ? 'red.300' : intent.priority === 'medium' ? 'yellow.300' : 'gray.300'}
                      borderWidth="2px"
                    >
                      <CardBody>
                        <HStack justify="space-between" align="start">
                          <VStack align="start" spacing={2} flex={1}>
                            <HStack spacing={2}>
                              <Badge colorScheme={getPriorityColor(intent.priority)} size="sm">
                                {intent.priority === 'high' ? '高' : intent.priority === 'medium' ? '中' : '低'}
                              </Badge>
                              <Text fontSize="sm" fontWeight="bold" color="blue.600">
                                {intent.category}
                              </Text>
                              {intent.isEdited && (
                                <Badge colorScheme="orange" size="sm">
                                  編集済み
                                </Badge>
                              )}
                              {intent.id.startsWith('manual-') && (
                                <Badge colorScheme="green" size="sm">
                                  手動追加
                                </Badge>
                              )}
                            </HStack>
                            
                            <Text fontSize="sm">
                              <strong>意図:</strong> {intent.intent}
                            </Text>
                            
                            {intent.description && (
                              <Text fontSize="xs" color="gray.600">
                                {intent.description}
                              </Text>
                            )}
                            
                            <HStack spacing={4}>
                              <Text fontSize="xs" color="gray.500">
                                予想時間: {intent.estimatedTime}分
                              </Text>
                              {intent.confidence && (
                                <Text fontSize="xs" color="gray.500">
                                  信頼度: {Math.round(intent.confidence * 100)}%
                                </Text>
                              )}
                            </HStack>
                          </VStack>
                          
                          <HStack>
                            <IconButton
                              icon={<EditIcon />}
                              size="sm"
                              variant="outline"
                              colorScheme="blue"
                              aria-label="編集"
                              onClick={() => onEditIntent(intent)}
                            />
                            <IconButton
                              icon={<DeleteIcon />}
                              size="sm"
                              variant="outline"
                              colorScheme="red"
                              aria-label="削除"
                              onClick={() => handleDeleteIntent(intent.id)}
                            />
                          </HStack>
                        </HStack>
                      </CardBody>
                    </Card>
                  ))}
                </VStack>
              ) : (
                <Alert status="warning">
                  <AlertIcon />
                  <AlertDescription>
                    質問意図がありません。テンプレートを選択するか、手動で追加してください。
                  </AlertDescription>
                </Alert>
              )}
            </VStack>
          </TabPanel>

          {/* テンプレート選択タブ */}
          <TabPanel px={0} py={4}>
            <VStack spacing={4} align="stretch">
              <Alert status="info">
                <AlertIcon />
                <AlertDescription fontSize="sm">
                  保存済みのテンプレートから選択できます。選択後も編集・追加が可能です。
                </AlertDescription>
              </Alert>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                {allTemplates.map((template) => (
                  <Card 
                    key={template.id} 
                    variant="outline"
                    cursor="pointer"
                    _hover={{ borderColor: 'blue.400', bg: 'blue.50' }}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <CardBody>
                      <VStack align="start" spacing={3}>
                        <HStack justify="space-between" w="100%">
                          <Text fontWeight="bold" fontSize="md">{template.name}</Text>
                          <Badge colorScheme={TEMPLATE_CATEGORIES[template.category as keyof typeof TEMPLATE_CATEGORIES]?.color}>
                            {TEMPLATE_CATEGORIES[template.category as keyof typeof TEMPLATE_CATEGORIES]?.label}
                          </Badge>
                        </HStack>
                        
                        <Text fontSize="sm" color="gray.600">{template.description}</Text>
                        
                        <Wrap spacing={2}>
                          {template.tags.map((tag, index) => (
                            <WrapItem key={index}>
                              <Tag size="sm" colorScheme="gray">{tag}</Tag>
                            </WrapItem>
                          ))}
                        </Wrap>
                        
                        <HStack spacing={4} fontSize="xs" color="gray.500">
                          <Text>質問数: {template.intents.length}</Text>
                          <Text>使用回数: {template.usageCount}回</Text>
                        </HStack>
                        
                        <Button
                          size="sm"
                          colorScheme="blue"
                          variant="ghost"
                          rightIcon={<CheckIcon />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTemplateSelect(template);
                          }}
                        >
                          このテンプレートを使用
                        </Button>
                      </VStack>
                    </CardBody>
                  </Card>
                ))}
              </SimpleGrid>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>

      <Divider />

      {/* テンプレート保存オプション */}
      <VStack spacing={3} align="stretch">
        <Text fontWeight="bold" fontSize="md">💾 テンプレート保存</Text>
        
        <HStack spacing={2}>
          <Switch
            isChecked={shouldSaveAsTemplate}
            onChange={(e) => onSaveAsTemplateToggle(e.target.checked)}
            colorScheme="green"
          />
          <Text fontSize="sm">質問テンプレートとして保存</Text>
        </HStack>
        
        {shouldSaveAsTemplate && (
          <FormControl>
            <FormLabel fontSize="sm">テンプレート名</FormLabel>
            <Input
              value={templateName}
              onChange={(e) => onTemplateNameChange(e.target.value)}
              placeholder="例：技術系ポジション用質問テンプレート"
              size="sm"
            />
          </FormControl>
        )}
        
        {shouldSaveAsTemplate && (
          <Alert status="success" size="sm">
            <AlertIcon />
            <AlertDescription fontSize="xs">
              このテンプレートは今後の面接で再利用できます。
            </AlertDescription>
          </Alert>
        )}
      </VStack>

      {/* アクションボタン */}
      <HStack justify="space-between">
        <Button
          leftIcon={<AttachmentIcon />}
          variant="outline"
          onClick={onPrevStep}
        >
          戻る
        </Button>
        
        <Button
          rightIcon={<LinkIcon />}
          colorScheme="blue"
          onClick={async () => {
            // テンプレート保存処理
            if (shouldSaveAsTemplate && templateName.trim()) {
              const success = await templateState.saveTemplate(
                templateName,
                `${localIntents.length}個の質問意図を含むテンプレート`,
                'custom',
                localIntents
              );
              
              if (success) {
                toast({
                  title: 'テンプレート保存完了',
                  description: `「${templateName}」として保存されました`,
                  status: 'success',
                  duration: 3000,
                  isClosable: true,
                });
              } else {
                toast({
                  title: 'テンプレート保存失敗',
                  description: '保存中にエラーが発生しました',
                  status: 'error',
                  duration: 3000,
                  isClosable: true,
                });
                return;
              }
            }
            
            onNextStep();
          }}
          isDisabled={localIntents.length === 0}
          isLoading={templateState.isLoading}
          loadingText="保存中..."
          size="lg"
        >
          リンク設定へ
        </Button>
      </HStack>

      {/* 手動追加モーダル */}
      <Modal isOpen={isManualModalOpen} onClose={onManualModalClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>質問意図を手動追加</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>カテゴリ</FormLabel>
                <Input
                  value={manualIntent.category}
                  onChange={(e) => setManualIntent({...manualIntent, category: e.target.value})}
                  placeholder="例：技術スキル"
                />
              </FormControl>
              
              <FormControl isRequired>
                <FormLabel>質問意図</FormLabel>
                <Textarea
                  value={manualIntent.intent}
                  onChange={(e) => setManualIntent({...manualIntent, intent: e.target.value})}
                  placeholder="例：実務で使用している技術スタックとその習熟度を確認したい"
                  rows={3}
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>説明</FormLabel>
                <Textarea
                  value={manualIntent.description}
                  onChange={(e) => setManualIntent({...manualIntent, description: e.target.value})}
                  placeholder="詳細な説明（任意）"
                  rows={2}
                />
              </FormControl>
              
              <HStack spacing={4} w="100%">
                <FormControl>
                  <FormLabel>優先度</FormLabel>
                  <Select
                    value={manualIntent.priority}
                    onChange={(e) => setManualIntent({...manualIntent, priority: e.target.value as 'high' | 'medium' | 'low'})}
                  >
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                  </Select>
                </FormControl>
                
                <FormControl>
                  <FormLabel>予想時間（分）</FormLabel>
                  <Input
                    type="number"
                    value={manualIntent.estimatedTime}
                    onChange={(e) => setManualIntent({...manualIntent, estimatedTime: parseInt(e.target.value) || 5})}
                    min={1}
                    max={30}
                  />
                </FormControl>
              </HStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onManualModalClose}>
              キャンセル
            </Button>
            <Button 
              colorScheme="blue" 
              onClick={handleManualAdd}
              isDisabled={!manualIntent.category || !manualIntent.intent}
            >
              追加
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </VStack>
  );
};