/**
 * エージェント用リアルタイムダッシュボード
 * 進行中の面接監視と完了済み面接の管理
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Progress,
  Button,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Icon,
  useColorModeValue,
  Divider,
  Avatar,
  Spinner,
  Alert,
  AlertIcon,
  Tooltip
} from '@chakra-ui/react';
import { 
  TimeIcon, 
  CheckCircleIcon, 
  ViewIcon, 
  DownloadIcon,
  RepeatClockIcon,
  StarIcon
} from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { ManagementApi } from '../services/api';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, staggerContainer, staggerItem } from '../motion/index';
import type { MeetingLink, InterviewSummary } from '@mensetsu-kun/shared/types';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

// ダッシュボード用の型定義
interface DashboardStats {
  totalInterviews: number;
  activeInterviews: number;
  completedToday: number;
  averageScore: number;
  completionRate: number;
}

interface ActiveInterview {
  id: string;
  candidateName: string;
  companyName: string;
  position: string;
  status: 'accessed' | 'in_progress' | 'paused';
  progress: number; // 0-100%
  timeElapsed: number; // 分
  estimatedTimeRemaining: number; // 分
  accessedAt: string;
  lastActivity: string;
}

export const AgentDashboard = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activeInterviews, setActiveInterviews] = useState<ActiveInterview[]>([]);
  const [recentCompletions, setRecentCompletions] = useState<InterviewSummary[]>([]);
  const [activeMeetingLinks, setActiveMeetingLinks] = useState<MeetingLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // カラーモード対応
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const activeColor = useColorModeValue('blue.50', 'blue.900');
  const successColor = useColorModeValue('green.50', 'green.900');

  /**
   * ダッシュボードデータの初期読み込み
   */
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        console.log('📊 ダッシュボードデータ読み込み開始...');

        // 並列でデータ取得
        const [feedbackResponse, meetingLinksData] = await Promise.all([
          ManagementApi.getAgentFeedbackList(),
          ManagementApi.getActiveMeetingLinks()
        ]);

        // 統計データの算出
        const totalInterviews = feedbackResponse.length;
        const averageScore = feedbackResponse.reduce((sum, interview) => sum + interview.overallScore, 0) / totalInterviews || 0;
        
        const dashboardStats: DashboardStats = {
          totalInterviews,
          activeInterviews: meetingLinksData.filter(link => link.status === 'active').length,
          completedToday: feedbackResponse.filter(interview => {
            const today = new Date().toDateString();
            return new Date(interview.interviewDate).toDateString() === today;
          }).length,
          averageScore: Math.round(averageScore * 10) / 10,
          completionRate: 85 // モック値
        };

        // アクティブな面接の生成（モック）
        const mockActiveInterviews: ActiveInterview[] = meetingLinksData
          .filter(link => link.status === 'active')
          .slice(0, 3)
          .map((link, index) => ({
            id: link.id,
            candidateName: link.candidateName || '候補者',
            companyName: link.companyInfo?.name || '企業名不明',
            position: link.companyInfo?.position || 'ポジション不明',
            status: index === 0 ? 'in_progress' : 'accessed',
            progress: index === 0 ? 45 : 15,
            timeElapsed: index === 0 ? 12 : 3,
            estimatedTimeRemaining: index === 0 ? 8 : 17,
            accessedAt: new Date(Date.now() - index * 30 * 60 * 1000).toISOString(),
            lastActivity: new Date(Date.now() - index * 5 * 60 * 1000).toISOString()
          }));

        // 最近の完了面接
        const mockRecentCompletions: InterviewSummary[] = feedbackResponse.slice(0, 3).map(interview => ({
          interviewId: `interview_${interview.candidateName}`,
          candidateName: interview.candidateName,
          position: '開発者', // モック
          companyName: '株式会社サンプル', // モック
          completedAt: interview.interviewDate + 'T15:30:00Z',
          overallScore: interview.overallScore,
          questionCount: interview.feedbacks.length,
          duration: 1200,
          answers: interview.feedbacks.map(feedback => ({
            questionText: '技術的な課題について教えてください',
            candidateAnswer: 'プロジェクトでの経験を説明します...',
            feedback: {
              ...feedback,
              answerId: `answer_${feedback.id}`,
              createdAt: new Date().toISOString()
            }
          }))
        }));

        setStats(dashboardStats);
        setActiveInterviews(mockActiveInterviews);
        setRecentCompletions(mockRecentCompletions);
        
        // MeetingLink型の統一のための変換
        const unifiedMeetingLinks = meetingLinksData.map(link => ({
          ...link,
          interviewId: `interview_${link.id}`,
          token: 'placeholder_token'
        }));
        setActiveMeetingLinks(unifiedMeetingLinks);
        setLastUpdate(new Date());

        console.log('✅ ダッシュボードデータ読み込み完了');

      } catch (error) {
        console.error('ダッシュボードデータ読み込みエラー:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();

    // 30秒毎に自動更新
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  /**
   * 面接状態のバッジカラー取得
   */
  const getStatusBadge = (status: ActiveInterview['status']) => {
    switch (status) {
      case 'in_progress':
        return <Badge colorScheme="green" variant="solid">実施中</Badge>;
      case 'accessed':
        return <Badge colorScheme="blue" variant="solid">アクセス済み</Badge>;
      case 'paused':
        return <Badge colorScheme="yellow" variant="solid">一時停止</Badge>;
      default:
        return <Badge colorScheme="gray">不明</Badge>;
    }
  };

  /**
   * 経過時間の表示フォーマット
   */
  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}分`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}時間${mins}分`;
  };

  if (isLoading) {
    return (
      <Box p={spacing.cardPadding} textAlign="center">
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" thickness="4px" />
          <Text {...textStyles.body}>ダッシュボードを読み込み中...</Text>
        </VStack>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        <Text>ダッシュボードデータの読み込みに失敗しました</Text>
      </Alert>
    );
  }

  return (
    <MotionBox {...staggerContainer} p={spacing.cardPadding} width="100%">
      <VStack spacing={spacing.sectionSpacing} align="stretch">
        
        {/* ヘッダー */}
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading as="h1" {...textStyles.heading} color="blue.600">
              エージェントダッシュボード
            </Heading>
            <Text {...textStyles.caption} color="gray.500">
              最終更新: {lastUpdate.toLocaleTimeString()}
            </Text>
          </VStack>
          
          <Button
            size="sm"
            variant="outline"
            leftIcon={<RepeatClockIcon />}
            onClick={() => window.location.reload()}
          >
            更新
          </Button>
        </HStack>

        {/* 統計カード */}
        <MotionBox {...staggerItem}>
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={4}>
            <Card bg={cardBg} borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel>総面接数</StatLabel>
                  <StatNumber color="blue.600">{stats.totalInterviews}</StatNumber>
                  <StatHelpText>累計実施数</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel>実施中</StatLabel>
                  <StatNumber color="green.600">{stats.activeInterviews}</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    アクティブ
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel>本日完了</StatLabel>
                  <StatNumber color="purple.600">{stats.completedToday}</StatNumber>
                  <StatHelpText>今日の実績</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel>平均スコア</StatLabel>
                  <StatNumber color="orange.600">{stats.averageScore}</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    全体平均
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </Grid>
        </MotionBox>

        {/* アクティブ面接 */}
        <MotionBox {...staggerItem}>
          <VStack spacing={4} align="stretch">
            <HStack justify="space-between">
              <Heading as="h2" size="lg" color="green.600">
                🔴 実施中の面接
              </Heading>
              <Badge colorScheme="green" fontSize="sm">
                {activeInterviews.length}件
              </Badge>
            </HStack>

            {activeInterviews.length === 0 ? (
              <Card bg={activeColor} borderColor="blue.200">
                <CardBody textAlign="center" py={8}>
                  <Text color="blue.600">現在実施中の面接はありません</Text>
                </CardBody>
              </Card>
            ) : (
              <VStack spacing={3}>
                <AnimatePresence>
                  {activeInterviews.map((interview, index) => (
                    <MotionCard
                      key={interview.id}
                      {...staggerItem}
                      custom={index}
                      w="full"
                      bg={cardBg}
                      borderColor={borderColor}
                      _hover={{ boxShadow: 'md' }}
                    >
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <HStack justify="space-between" align="center">
                            <HStack spacing={3}>
                              <Avatar size="sm" name={interview.candidateName} />
                              <VStack align="start" spacing={0}>
                                <Text fontWeight="bold">{interview.candidateName}</Text>
                                <Text fontSize="sm" color="gray.600">
                                  {interview.companyName} - {interview.position}
                                </Text>
                              </VStack>
                            </HStack>
                            
                            <HStack spacing={2}>
                              {getStatusBadge(interview.status)}
                              <Tooltip label="詳細表示">
                                <Button size="sm" variant="ghost" leftIcon={<ViewIcon />}>
                                  監視
                                </Button>
                              </Tooltip>
                            </HStack>
                          </HStack>

                          <VStack spacing={3} align="stretch">
                            <HStack justify="space-between">
                              <Text fontSize="sm" color="gray.600">進捗状況</Text>
                              <Text fontSize="sm" fontWeight="bold">{interview.progress}%</Text>
                            </HStack>
                            
                            <Progress 
                              value={interview.progress} 
                              colorScheme="green" 
                              size="sm" 
                              borderRadius="full"
                            />

                            <HStack justify="space-between" fontSize="xs" color="gray.500">
                              <HStack spacing={1}>
                                <TimeIcon />
                                <Text>経過: {formatDuration(interview.timeElapsed)}</Text>
                              </HStack>
                              <Text>残り約: {formatDuration(interview.estimatedTimeRemaining)}</Text>
                            </HStack>
                          </VStack>
                        </VStack>
                      </CardBody>
                    </MotionCard>
                  ))}
                </AnimatePresence>
              </VStack>
            )}
          </VStack>
        </MotionBox>

        {/* 最近の完了面接 */}
        <MotionBox {...staggerItem}>
          <VStack spacing={4} align="stretch">
            <HStack justify="space-between">
              <Heading as="h2" size="lg" color="purple.600">
                ✅ 最近の完了面接
              </Heading>
              <Button size="sm" variant="outline" leftIcon={<ViewIcon />}>
                すべて表示
              </Button>
            </HStack>

            <VStack spacing={3}>
              {recentCompletions.map((completion, index) => (
                <MotionCard
                  key={completion.interviewId}
                  {...staggerItem}
                  custom={index}
                  w="full"
                  bg={successColor}
                  borderColor="green.200"
                >
                  <CardBody>
                    <HStack justify="space-between" align="center">
                      <HStack spacing={3}>
                        <Avatar size="sm" name={completion.candidateName} />
                        <VStack align="start" spacing={0}>
                          <Text fontWeight="bold">{completion.candidateName}</Text>
                          <Text fontSize="sm" color="gray.600">
                            {completion.companyName} - {completion.position}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {new Date(completion.completedAt).toLocaleString()}
                          </Text>
                        </VStack>
                      </HStack>
                      
                      <VStack align="end" spacing={1}>
                        <HStack spacing={2}>
                          <StarIcon color="yellow.500" boxSize={4} />
                          <Text fontWeight="bold" color="green.600">
                            {completion.overallScore}/10
                          </Text>
                        </HStack>
                        <HStack spacing={2}>
                          <Button size="xs" colorScheme="green" variant="solid">
                            レポート
                          </Button>
                          <Button size="xs" variant="outline" leftIcon={<DownloadIcon />}>
                            DL
                          </Button>
                        </HStack>
                      </VStack>
                    </HStack>
                  </CardBody>
                </MotionCard>
              ))}
            </VStack>
          </VStack>
        </MotionBox>

      </VStack>
    </MotionBox>
  );
};