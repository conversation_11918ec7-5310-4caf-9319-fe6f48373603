import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import authService from '../../candidate/services/authService';

export const useAuth = (requiredRole?: 'agent' | 'interviewer') => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);

  // Khai báo candidateDomain một lần
  const candidateDomain = process.env.NEXT_CANDIDATE_PUBLIC_URL || 'http://localhost:3000';

  useEffect(() => {
    const checkAuth = async () => {
    //   console.log('🔍 checkAuth started');
    //   console.log('📍 Current pathname:', router.pathname);

      try {
        // Lấy token từ cookie
        const cookies = document.cookie.split(';');
        const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('access_token='));
        const token = tokenCookie ? tokenCookie.split('=')[1] : null;

        // console.log('🔍 Token check results:');
        // console.log('  - Cookie token:', token);
        // console.log('  - All cookies:', document.cookie);

        if (!token) {
        //   console.log('❌ No token found, redirecting to login');
          window.location.href = `${candidateDomain}/demo-login`;
          return;
        }

        // console.log('✅ Token found:', token);
        const currentUser = await authService.getCurrentUser();
        // console.log('👤 User from API:', currentUser);

        if (!currentUser) {
        //   console.log('❌ No user found, clearing token');
          document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          window.location.href = `${candidateDomain}/demo-login`;
          return;
        }

        // console.log('✅ User authenticated:', currentUser);
        if (requiredRole && currentUser.role !== requiredRole) {
        //   console.log('❌ Wrong role, redirecting');
          window.location.href = `${candidateDomain}/demo-login`;
          return;
        }

        setIsAuthenticated(true);
        setUser(currentUser);
        // console.log('✅ Auth successful');

      } catch (error) {
        console.error('❌ Auth check error:', error);
        document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        window.location.href = `${candidateDomain}/demo-login`;
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router, requiredRole, candidateDomain]);

  return { isLoading, isAuthenticated, user };
};
