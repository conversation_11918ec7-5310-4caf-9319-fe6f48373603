export interface CandidateApiResponse {
  id: string;
  userId: string;
  nameKana: string;
  age: number;
  employmentStatus: 'employed' | 'unemployed' | 'student';
  company?: string;
  position?: string;
  address?: string;
  hasResume: boolean;
  hasCareerHistory: boolean;
  profile?: any;
  mainSkill?: string;
  otherSkill?: string;
  experience?: string;
  education?: string;
  language?: string;
  otherInfo?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CandidateInfo {
  id: string;
  name: string;
  nameKana: string;
  age: number;
  currentStatus: string;
  currentEmployer?: string;
  previousEmployer?: string;
  position?: string;
  address?: string;
  hasResume: boolean;
  hasCareerHistory: boolean;
  activeLinks: any[];
}
