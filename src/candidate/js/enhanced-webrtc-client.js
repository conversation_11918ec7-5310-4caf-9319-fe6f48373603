// Enhanced WebRTC Client - Continuation of methods
// This file contains the remaining methods for the EnhancedWebRTCClient class

// WebRTC Peer Connection Methods
setupPeerConnection() {
  this.log('info', 'Setting up peer connection');
  
  this.state.peerConnection = new RTCPeerConnection({
    iceServers: this.config.iceServers
  });
  
  const pc = this.state.peerConnection;
  
  // Add local stream tracks
  if (this.state.localStream) {
    this.state.localStream.getTracks().forEach(track => {
      pc.addTrack(track, this.state.localStream);
      this.log('debug', `Added ${track.kind} track to peer connection`);
    });
  }
  
  // Setup remote stream
  this.state.remoteStream = new MediaStream();
  this.elements.remoteVideo.srcObject = this.state.remoteStream;
  
  // Event handlers
  pc.ontrack = (event) => {
    this.log('info', `Received remote ${event.track.kind} track`);
    event.streams[0].getTracks().forEach(track => {
      this.state.remoteStream.addTrack(track);
    });
    this.updateConnectionInfo();
  };
  
  pc.onicecandidate = (event) => {
    if (event.candidate) {
      this.log('debug', 'Generated ICE candidate', {
        candidate: event.candidate.candidate,
        sdpMid: event.candidate.sdpMid
      });
      
      this.sendWebSocketMessage({
        type: 'webrtc_candidate',
        candidate: event.candidate,
        sessionId: this.config.sessionId
      });
    } else {
      this.log('info', 'ICE candidate gathering completed');
    }
  };
  
  pc.onconnectionstatechange = () => {
    const state = pc.connectionState;
    this.log('info', `Connection state changed: ${state}`);
    this.updateConnectionInfo();
    
    switch (state) {
      case 'connected':
        this.updateStatus('Connected', 'connected');
        this.startStatsCollection();
        break;
      case 'disconnected':
        this.updateStatus('Disconnected', 'disconnected');
        this.stopStatsCollection();
        break;
      case 'failed':
        this.updateStatus('Connection failed', 'error');
        this.stopStatsCollection();
        break;
      case 'connecting':
        this.updateStatus('Connecting...', 'connecting');
        break;
    }
    
    this.updateControlStates();
  };
  
  pc.oniceconnectionstatechange = () => {
    const state = pc.iceConnectionState;
    this.log('info', `ICE connection state changed: ${state}`);
    this.updateConnectionInfo();
  };
  
  pc.onsignalingstatechange = () => {
    const state = pc.signalingState;
    this.log('info', `Signaling state changed: ${state}`);
    this.updateConnectionInfo();
  };
}

// Handle WebRTC answer
async handleAnswer(answer) {
  try {
    this.log('info', 'Received WebRTC answer');
    
    if (!this.state.peerConnection) {
      throw new Error('Peer connection not initialized');
    }
    
    await this.state.peerConnection.setRemoteDescription(answer);
    this.state.remoteDescSet = true;
    this.log('success', 'Remote description set successfully');
    
    // Process queued ICE candidates
    await this.processQueuedCandidates();
    
  } catch (error) {
    this.log('error', 'Failed to handle WebRTC answer', error);
    this.updateStatus('Failed to process answer', 'error');
  }
}

// Handle ICE candidate
async handleCandidate(candidate) {
  try {
    this.log('debug', 'Received ICE candidate', candidate);
    
    if (!this.state.peerConnection) {
      this.log('warning', 'Peer connection not ready, queueing candidate');
      this.state.candidateQueue.push(candidate);
      return;
    }
    
    if (this.state.remoteDescSet) {
      await this.state.peerConnection.addIceCandidate(candidate);
      this.log('debug', 'ICE candidate added successfully');
    } else {
      this.log('debug', 'Remote description not set, queueing candidate');
      this.state.candidateQueue.push(candidate);
    }
    
  } catch (error) {
    this.log('error', 'Failed to add ICE candidate', error);
  }
}

// Process queued ICE candidates
async processQueuedCandidates() {
  this.log('info', `Processing ${this.state.candidateQueue.length} queued ICE candidates`);
  
  for (const candidate of this.state.candidateQueue) {
    try {
      await this.state.peerConnection.addIceCandidate(candidate);
      this.log('debug', 'Queued ICE candidate added successfully');
    } catch (error) {
      this.log('error', 'Failed to add queued ICE candidate', error);
    }
  }
  
  this.state.candidateQueue = [];
}

// Get user media with enhanced error handling
async getUserMedia() {
  try {
    const videoQuality = this.elements.videoQualitySelect.value;
    const audioEnabled = this.elements.audioEnabledSelect.value === 'true';
    
    let videoConstraints = false;
    if (videoQuality !== 'disabled') {
      const qualitySettings = {
        low: { width: 320, height: 240, frameRate: 15 },
        medium: { width: 640, height: 480, frameRate: 30 },
        high: { width: 1280, height: 720, frameRate: 30 }
      };
      
      videoConstraints = {
        ...qualitySettings[videoQuality],
        facingMode: this.state.currentCamera
      };
    }
    
    const constraints = {
      video: videoConstraints,
      audio: audioEnabled
    };
    
    this.log('info', 'Requesting user media', constraints);
    
    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    this.log('success', `Media acquired: ${stream.getTracks().length} tracks`);
    
    return stream;
    
  } catch (error) {
    this.log('error', 'Failed to get user media', {
      name: error.name,
      message: error.message
    });
    
    // Provide user-friendly error messages
    let userMessage = 'Failed to access camera/microphone';
    switch (error.name) {
      case 'NotAllowedError':
        userMessage = 'Camera/microphone access denied. Please allow access and try again.';
        break;
      case 'NotFoundError':
        userMessage = 'No camera/microphone found. Please connect a device and try again.';
        break;
      case 'NotReadableError':
        userMessage = 'Camera/microphone is being used by another application.';
        break;
      case 'OverconstrainedError':
        userMessage = 'Camera/microphone does not support the requested settings.';
        break;
    }
    
    this.updateStatus(userMessage, 'error');
    throw error;
  }
}

// Main test methods
async startTest() {
  try {
    this.log('info', 'Starting WebRTC test');
    this.updateStatus('Starting test...', 'connecting');
    this.state.isActive = true;
    this.state.connectionStartTime = Date.now();
    this.updateControlStates();
    
    // Get user media
    this.state.localStream = await this.getUserMedia();
    this.elements.localVideo.srcObject = this.state.localStream;
    this.log('success', 'Local media stream acquired');
    
    // Connect WebSocket
    await this.connectWebSocket();
    
    // Setup peer connection
    this.setupPeerConnection();
    
    // Create and send offer
    const offer = await this.state.peerConnection.createOffer();
    await this.state.peerConnection.setLocalDescription(offer);
    
    this.sendWebSocketMessage({
      type: 'webrtc_offer',
      offer,
      sessionId: this.config.sessionId
    });
    
    this.log('success', 'WebRTC offer sent');
    this.updateStatus('Waiting for answer...', 'connecting');
    
  } catch (error) {
    this.log('error', 'Failed to start test', error);
    this.updateStatus('Failed to start test', 'error');
    this.stopTest();
  }
}

async stopTest() {
  this.log('info', 'Stopping WebRTC test');
  this.state.isActive = false;
  
  // Stop stats collection
  this.stopStatsCollection();
  
  // Close peer connection
  if (this.state.peerConnection) {
    this.state.peerConnection.close();
    this.state.peerConnection = null;
    this.log('info', 'Peer connection closed');
  }
  
  // Close WebSocket
  if (this.state.ws) {
    this.state.ws.close();
    this.state.ws = null;
    this.log('info', 'WebSocket connection closed');
  }
  
  // Stop local stream
  if (this.state.localStream) {
    this.state.localStream.getTracks().forEach(track => {
      track.stop();
      this.log('debug', `Stopped ${track.kind} track`);
    });
    this.state.localStream = null;
  }
  
  // Clear video elements
  this.elements.localVideo.srcObject = null;
  this.elements.remoteVideo.srcObject = null;
  
  // Reset state
  this.state.remoteStream = null;
  this.state.candidateQueue = [];
  this.state.remoteDescSet = false;
  this.state.connectionStartTime = null;
  
  // Clear intervals and timeouts
  this.intervals.forEach(interval => clearInterval(interval));
  this.timeouts.forEach(timeout => clearTimeout(timeout));
  this.intervals.clear();
  this.timeouts.clear();
  
  this.updateStatus('Test stopped', 'disconnected');
  this.updateConnectionInfo();
  this.updateControlStates();
  
  this.log('info', 'WebRTC test stopped successfully');
}

async restartTest() {
  this.log('info', 'Restarting WebRTC test');
  await this.stopTest();
  
  // Wait a moment before restarting
  setTimeout(() => {
    this.startTest();
  }, 1000);
}
