/**
 * ログイン・テストページ
 * エージェント・面接官: 本番でも使用するログイン機能
 * 候補者セクション: 開発・テスト用状態シミュレーション
 */
import React, { useState } from 'react';
import {
  Box,
  VStack,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Heading,
  Badge,
  List,
  ListItem,
  ListIcon,
  useToast,
  Input,
  FormControl,
  FormLabel,
  Container,
  Divider,
  HStack,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import { useEffect } from 'react';
import { CheckCircleIcon, StarIcon } from '@chakra-ui/icons';
import { useRouter } from 'next/router';
import { AccessibilityHelper } from '../components/AccessibilityHelper';
import authService from '../services/authService';
import config from '../config';

const DemoLoginPage: React.FC = () => {
  const router = useRouter();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [manualEmail, setManualEmail] = useState('');
  const [manualPassword, setManualPassword] = useState('');

  // Kiểm tra nếu đã login thì redirect
  useEffect(() => {
    const checkAlreadyLoggedIn = async () => {
      try {
        // Lấy token từ cookie
        const cookies = document.cookie.split(';');
        const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('access_token='));
        const token = tokenCookie ? tokenCookie.split('=')[1] : null;

        if (token) {
          // console.log('�� Already logged in, checking user role...');
          const currentUser = await authService.getCurrentUser();

          if (currentUser) {
            // console.log('✅ User found, redirecting based on role:', currentUser.role);

            switch (currentUser.role) {
              case 'agent':
                const managementDomain = process.env.NEXT_PUBLIC_MANAGEMENT_URL || 'http://localhost:3001';
                window.location.href = managementDomain;
                break;
              case 'interviewer':
                router.push('/interviewer-dashboard');
                break;
              default:
                // Role không xác định, ở lại trang login
                console.log('❓ Unknown role, staying on login page');
            }
          }
        }
      } catch (error) {
        // console.error('Error checking login status:', error);
        // Nếu có lỗi, xóa token và ở lại trang login
        document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      }
    };

    checkAlreadyLoggedIn();
  }, [router]);

  const handleQuickLogin = async (accountType: string) => {
    setIsLoading(true);

    // デモアカウントの認証情報
    const demoAccounts: Record<string, {email: string, password: string}> = {
      agent: {
        email: '<EMAIL>',
        password: 'demo123'
      },
      interviewer: {
        email: '<EMAIL>',
        password: 'demo123'
      }
    };
    
    const account = demoAccounts[accountType];
    if (!account) {
      toast({
        title: 'エラー',
        description: '不正なアカウントタイプです',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      setIsLoading(false);
      return;
    }
    
    try {
      const result = await authService.login({
        email: account.email,
        password: account.password
      });
      
      toast({
        title: 'ログイン成功',
        description: `${result.user.profile.name}としてログインしました`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // ロールに応じたページへリダイレクト
      switch (result.user.role) {
        case 'agent':
          // エージェントは管理画面へ移動
          window.open(config.urls.management, '_self');
          break;
        case 'interviewer':
          // 面接官ダッシュボード（実装予定）
          router.push('/interviewer-dashboard');
          break;
        default:
          toast({
            title: '未対応のアカウント',
            description: 'このアカウントタイプはサポートされていません',
            status: 'warning',
            duration: 3000,
            isClosable: true,
          });
      }
    } catch (error: any) {
      toast({
        title: 'ログインエラー',
        description: error.message || 'ログインに失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualLogin = async () => {
    setIsLoading(true);
    
    if (!manualEmail || !manualPassword) {
      toast({
        title: '入力エラー',
        description: 'メールアドレスとパスワードを入力してください',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      setIsLoading(false);
      return;
    }

    try {
      const result = await authService.login({
        email: manualEmail,
        password: manualPassword
      });

      // アクセストークンが取得できたかチェック
      if (!result.accessToken) {
        return;
      }

      const user = await authService.getCurrentUser();

      toast({
        title: 'ログイン成功',
        description: `${user?.profile_name}としてログインしました`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // ロールに応じたリダイレクト
      switch (user?.role) {
        case 'agent':
          window.open(config.urls.management, '_self');
          break;
        case 'interviewer':
          router.push('/interviewer-dashboard');
          break;
        default:
          toast({
            title: '未対応のアカウント',
            description: 'このアカウントタイプはサポートされていません',
            status: 'warning',
            duration: 3000,
            isClosable: true,
          });
      }
    } catch (error: any) {
      toast({
        title: 'ログインエラー',
        description: error.message || 'メールアドレスまたはパスワードが正しくありません',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box minH="100vh" bg="gray.50" py={{ base: 4, md: 8 }}>
      {/* アクセシビリティヘルパー */}
      <AccessibilityHelper />
      
      <Container maxW="container.xl">
        <VStack spacing={8} id="main-content" tabIndex={-1}>
          {/* ヘッダー */}
          <VStack spacing={2} textAlign="center">
            <Heading size="xl" color="primary.700">
              面接君 - ログイン・テストページ
            </Heading>
            <Text color="gray.600">
              エージェント・面接官はログイン、候補者セクションはテスト用です
            </Text>
          </VStack>

          <Tabs isFitted variant="enclosed" w="full">
            <TabList mb="1em">
              <Tab>エージェント・面接官ログイン</Tab>
              <Tab>候補者テスト機能</Tab>
            </TabList>
            
            <TabPanels>
              {/* エージェント・面接官ログイン */}
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{ base: 4, md: 6 }}>

                  {/* エージェントカード */}
                  <Card>
                    <CardHeader>
                      <VStack align="start" spacing={{ base: 2, md: 3 }}>
                        <Badge colorScheme="green" fontSize={{ base: "xs", md: "sm" }}>エージェント</Badge>
                        <Heading size={{ base: "sm", md: "md" }}>佐藤 健太</Heading>
                        <Text fontSize={{ base: "xs", md: "sm" }} color="gray.600">
                          32歳 / キャリアコンサルタント
                        </Text>
                      </VStack>
                    </CardHeader>
                    
                    <CardBody>
                      <VStack align="start" spacing={3}>
                        <Text fontSize="sm">
                          効率的な面接対策サポートを求めるエージェント
                        </Text>
                        <Divider />
                        <Text fontSize="xs" fontWeight="bold" color="gray.700">
                          テストシナリオ：
                        </Text>
                        <List spacing={1} fontSize={{ base: "2xs", md: "xs" }}>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            求職者向けリンク発行
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            面接設定のカスタマイズ
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            結果レポートの確認
                          </ListItem>
                        </List>
                      </VStack>
                    </CardBody>
                    
                    <CardFooter>
                      <Button
                        colorScheme="green"
                        w="full"
                        onClick={() => handleQuickLogin('agent')}
                        isLoading={isLoading}
                      >
                        このアカウントでログイン
                      </Button>
                    </CardFooter>
                  </Card>

                  {/* 面接官カード */}
                  <Card>
                    <CardHeader>
                      <VStack align="start" spacing={{ base: 2, md: 3 }}>
                        <Badge colorScheme="purple" fontSize={{ base: "xs", md: "sm" }}>企業担当者</Badge>
                        <Heading size={{ base: "sm", md: "md" }}>鈴木 雅子</Heading>
                        <Text fontSize={{ base: "xs", md: "sm" }} color="gray.600">
                          38歳 / 人事部 採用課長
                        </Text>
                      </VStack>
                    </CardHeader>
                    
                    <CardBody>
                      <VStack align="start" spacing={3}>
                        <Text fontSize="sm">
                          面接スキル向上と部下育成を目指す管理職
                        </Text>
                        <Divider />
                        <Text fontSize="xs" fontWeight="bold" color="gray.700">
                          テストシナリオ：
                        </Text>
                        <List spacing={1} fontSize={{ base: "2xs", md: "xs" }}>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            セルフ面接練習
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            AIからのコーチング
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            チーム分析ダッシュボード
                          </ListItem>
                        </List>
                      </VStack>
                    </CardBody>
                    
                    <CardFooter>
                      <Button
                        colorScheme="purple"
                        w="full"
                        onClick={() => handleQuickLogin('interviewer')}
                        isLoading={isLoading}
                      >
                        このアカウントでログイン
                      </Button>
                    </CardFooter>
                  </Card>
                </SimpleGrid>
                
                {/* 手動ログインフォーム */}
                <Card mt={6}>
                  <CardHeader>
                    <Heading size="md">手動ログイン</Heading>
                    <Text fontSize="sm" color="gray.600">
                      メールアドレスとパスワードでログイン
                    </Text>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4}>
                      <FormControl>
                        <FormLabel>メールアドレス</FormLabel>
                        <Input
                          type="email"
                          value={manualEmail}
                          onChange={(e) => setManualEmail(e.target.value)}
                          placeholder="例: <EMAIL>"
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel>パスワード</FormLabel>
                        <Input
                          type="password"
                          value={manualPassword}
                          onChange={(e) => setManualPassword(e.target.value)}
                          placeholder="パスワード"
                        />
                      </FormControl>
                      <Button
                        colorScheme="blue"
                        w="full"
                        onClick={handleManualLogin}
                        isLoading={isLoading}
                      >
                        ログイン
                      </Button>
                    </VStack>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* 候補者テスト機能 */}
              <TabPanel>
                <VStack spacing={6} maxW="2xl" mx="auto">
                  <Box textAlign="center">
                    <Heading size="md" color="orange.600" mb={2}>
                      開発・テスト用機能
                    </Heading>
                    <Text fontSize="sm" color="gray.600">
                      候補者は通常エージェントから送られたリンクでアクセスします
                    </Text>
                  </Box>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                    {/* 有効なリンクテスト */}
                    <Card>
                      <CardHeader>
                        <Badge colorScheme="green" mb={2}>有効リンク</Badge>
                        <Heading size="sm">エージェントリンク（有効）</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text fontSize="sm" mb={3}>
                          期限内の有効なエージェントリンクにアクセスした状態をテスト
                        </Text>
                        <List spacing={1} fontSize="xs">
                          <ListItem>• トークン入力画面が表示される</ListItem>
                          <ListItem>• 正しいトークンで面接開始</ListItem>
                        </List>
                      </CardBody>
                      <CardFooter>
                        <Button
                          colorScheme="green"
                          size="sm"
                          w="full"
                          onClick={() => router.push('/interview-prep?linkId=valid_link_123&status=active')}
                        >
                          有効リンクをテスト
                        </Button>
                      </CardFooter>
                    </Card>

                    {/* 期限切れリンクテスト */}
                    <Card>
                      <CardHeader>
                        <Badge colorScheme="red" mb={2}>期限切れ</Badge>
                        <Heading size="sm">エージェントリンク（期限切れ）</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text fontSize="sm" mb={3}>
                          期限切れのエージェントリンクにアクセスした状態をテスト
                        </Text>
                        <List spacing={1} fontSize="xs">
                          <ListItem>• 期限切れメッセージが表示される</ListItem>
                          <ListItem>• エージェントに連絡するよう案内</ListItem>
                        </List>
                      </CardBody>
                      <CardFooter>
                        <Button
                          colorScheme="red"
                          size="sm"
                          w="full"
                          onClick={() => router.push('/interview-prep?linkId=expired_link_456&status=expired')}
                        >
                          期限切れリンクをテスト
                        </Button>
                      </CardFooter>
                    </Card>

                    {/* 無効リンクテスト */}
                    <Card>
                      <CardHeader>
                        <Badge colorScheme="gray" mb={2}>無効</Badge>
                        <Heading size="sm">無効なリンク</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text fontSize="sm" mb={3}>
                          存在しないまたは無効なリンクにアクセスした状態をテスト
                        </Text>
                        <List spacing={1} fontSize="xs">
                          <ListItem>• 無効リンクエラーが表示される</ListItem>
                          <ListItem>• 適切な案内メッセージを表示</ListItem>
                        </List>
                      </CardBody>
                      <CardFooter>
                        <Button
                          colorScheme="gray"
                          size="sm"
                          w="full"
                          onClick={() => router.push('/interview-prep?linkId=invalid_link_999&status=invalid')}
                        >
                          無効リンクをテスト
                        </Button>
                      </CardFooter>
                    </Card>

                    {/* test-dataページテスト */}
                    <Card>
                      <CardHeader>
                        <Badge colorScheme="blue" mb={2}>開発用</Badge>
                        <Heading size="sm">面接データプレビュー</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text fontSize="sm" mb={3}>
                          エージェントが設定した面接データのプレビュー画面
                        </Text>
                        <List spacing={1} fontSize="xs">
                          <ListItem>• 企業情報とシナリオ表示</ListItem>
                          <ListItem>• 面接練習の開始リンク</ListItem>
                        </List>
                      </CardBody>
                      <CardFooter>
                        <Button
                          colorScheme="blue"
                          size="sm"
                          w="full"
                          onClick={() => router.push('/test-data')}
                        >
                          プレビューページを表示
                        </Button>
                      </CardFooter>
                    </Card>
                  </SimpleGrid>

                  <Box p={4} bg="orange.50" borderRadius="md" w="full">
                    <Text fontSize="sm" color="orange.700" fontWeight="bold" mb={2}>
                      注意事項：
                    </Text>
                    <List spacing={1} fontSize="xs" color="orange.600">
                      <ListItem>• これらは開発・テスト専用機能です</ListItem>
                      <ListItem>• 実際の候補者はエージェントから直接送られるリンクを使用します</ListItem>
                      <ListItem>• 本番環境では候補者がこのページにアクセスすることはありません</ListItem>
                    </List>
                  </Box>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>

          {/* フッター情報 */}
          <Box textAlign="center" color="gray.600" fontSize="sm">
            <Text>
              エージェント・面接官：本番ログイン機能 / 候補者テスト：開発用機能
            </Text>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default DemoLoginPage;