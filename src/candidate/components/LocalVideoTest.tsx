import React, { useEffect, useRef, useState } from 'react';
import { Box, VStack, Text, Badge, Button } from '@chakra-ui/react';
import { webrtcService } from '../services/webrtcService';

export const LocalVideoTest: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [hasStream, setHasStream] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  
  useEffect(() => {
    // Update video element when local stream changes
    const updateVideo = () => {
      if (videoRef.current && webrtcService.isLocalStreamReady()) {
        const success = webrtcService.attachLocalStreamToVideo(videoRef.current);
        if (success) {
          videoRef.current.play().catch(console.error);
          setHasStream(true);
        }
      }
    };

    // Check periodically for stream updates
    const interval = setInterval(updateVideo, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const initializeLocalVideo = async () => {
    try {
      const success = await webrtcService.initializeConnection('local_test', false);
      if (success) {
        setIsInitialized(true);
        console.log('WebRTC initialized for local video test');
        
        // Update video element
        setTimeout(() => {
          if (videoRef.current && webrtcService.isLocalStreamReady()) {
            const success = webrtcService.attachLocalStreamToVideo(videoRef.current);
            if (success) {
              videoRef.current.play().catch(console.error);
              setHasStream(true);
            }
          }
        }, 500);
      }
    } catch (error) {
      console.error('Failed to initialize local video:', error);
    }
  };

  const stopLocalVideo = () => {
    webrtcService.cleanup();
    setHasStream(false);
    setIsInitialized(false);
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  return (
    <VStack spacing={4} p={4}>
      <Text fontSize="lg" fontWeight="bold">
        Local Video Test
      </Text>
      
      <Badge colorScheme={hasStream ? 'green' : 'gray'} size="lg">
        {hasStream ? 'Stream Active' : 'No Stream'}
      </Badge>
      
      <Box
        w="400px"
        h="300px"
        bg="black"
        borderRadius="md"
        overflow="hidden"
        position="relative"
      >
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            transform: 'scaleX(-1)', // Mirror effect
          }}
        />
        
        {!hasStream && (
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            color="white"
            textAlign="center"
          >
            <Text fontSize="sm">
              {isInitialized ? 'Initializing camera...' : 'Camera not initialized'}
            </Text>
          </Box>
        )}
      </Box>
      
      <VStack spacing={2}>
        <Button
          colorScheme="blue"
          onClick={initializeLocalVideo}
          disabled={isInitialized}
        >
          Start Local Video
        </Button>
        
        <Button
          colorScheme="red"
          onClick={stopLocalVideo}
          disabled={!isInitialized}
        >
          Stop Video
        </Button>
      </VStack>
    </VStack>
  );
}; 