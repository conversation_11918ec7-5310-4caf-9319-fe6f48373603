import React, { useEffect, useState, useRef, useCallback } from 'react';
import { 
  Box, 
  Button, 
  Text, 
  VStack, 
  HStack, 
  Badge, 
  Progress,
  Divider,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Textarea,
  Input,
  FormControl,
  FormLabel,
  Select,
  Switch,
  IconButton,
  Tooltip,
  Card,
  CardHeader,
  CardBody,
  Heading,
  SimpleGrid
} from '@chakra-ui/react';
import { 
  FiPlay, 
  FiSquare, 
  FiRefreshCw, 
  FiVideo, 
  FiVideoOff, 
  FiMic, 
  FiMicOff,
  FiCamera,
  FiWifi,
  FiWifiOff,
  FiDownload,
  FiTrash2,
  FiActivity,
  FiSettings
} from 'react-icons/fi';
import { webrtcService } from '../services/webrtcService';
import { interviewService } from '../services/interviewService';

// Enhanced interfaces for better type safety
interface LogEntry {
  timestamp: Date;
  level: 'info' | 'success' | 'warning' | 'error' | 'debug';
  message: string;
  data?: any;
}

interface ConnectionStats {
  bytesSent: number;
  bytesReceived: number;
  packetsLost: number;
  roundTripTime: number;
  connectionTime: number;
  reconnectCount: number;
  errorCount: number;
}

interface MediaInfo {
  localTracks: number;
  remoteTracks: number;
  videoResolution: string;
  frameRate: number;
  videoEnabled: boolean;
  audioEnabled: boolean;
}

interface TestConfiguration {
  sessionId: string;
  videoQuality: 'low' | 'medium' | 'high';
  audioEnabled: boolean;
  autoReconnect: boolean;
  maxReconnectAttempts: number;
}

export const EnhancedWebRTCTest: React.FC = () => {
  // Core state
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionState, setConnectionState] = useState<string>('disconnected');
  const [iceState, setIceState] = useState<string>('new');
  const [signalingState, setSignalingState] = useState<string>('stable');
  
  // Enhanced logging
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const maxLogs = 500;
  
  // Configuration
  const [config, setConfig] = useState<TestConfiguration>({
    sessionId: `test_${Date.now()}`,
    videoQuality: 'medium',
    audioEnabled: true,
    autoReconnect: true,
    maxReconnectAttempts: 3
  });
  
  // Stats and monitoring
  const [connectionStats, setConnectionStats] = useState<ConnectionStats>({
    bytesSent: 0,
    bytesReceived: 0,
    packetsLost: 0,
    roundTripTime: 0,
    connectionTime: 0,
    reconnectCount: 0,
    errorCount: 0
  });
  
  const [mediaInfo, setMediaInfo] = useState<MediaInfo>({
    localTracks: 0,
    remoteTracks: 0,
    videoResolution: '-',
    frameRate: 0,
    videoEnabled: true,
    audioEnabled: true
  });
  
  const [uptime, setUptime] = useState('00:00:00');
  const [startTime, setStartTime] = useState<Date | null>(null);
  
  // Refs for cleanup
  const statsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const uptimeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Toast notifications
  const toast = useToast();

  // Enhanced logging with structured data
  const addLog = useCallback((level: LogEntry['level'], message: string, data?: any) => {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      data
    };
    
    setLogs(prev => {
      const newLogs = [...prev, logEntry];
      return newLogs.slice(-maxLogs); // Keep only recent logs
    });
    
    // Update error count
    if (level === 'error') {
      setConnectionStats(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));
    }
    
    // Show toast notifications for important events
    if (level === 'error') {
      toast({
        title: 'Error',
        description: message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } else if (level === 'success') {
      toast({
        title: 'Success',
        description: message,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  }, [toast]);

  // Uptime tracking
  const startUptimeCounter = useCallback(() => {
    if (uptimeIntervalRef.current) return;
    
    const startTime = Date.now();
    setStartTime(new Date());
    
    uptimeIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const hours = Math.floor(elapsed / 3600000);
      const minutes = Math.floor((elapsed % 3600000) / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      
      setUptime(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    }, 1000);
  }, []);

  const stopUptimeCounter = useCallback(() => {
    if (uptimeIntervalRef.current) {
      clearInterval(uptimeIntervalRef.current);
      uptimeIntervalRef.current = null;
    }
    setUptime('00:00:00');
    setStartTime(null);
  }, []);

  // Stats collection
  const startStatsCollection = useCallback(() => {
    if (statsIntervalRef.current) return;
    
    statsIntervalRef.current = setInterval(async () => {
      try {
        // Get connection info from webrtcService
        const connectionInfo = webrtcService.getConnectionInfo();
        
        // Update connection states
        if (connectionInfo.peerConnection) {
          setConnectionState(connectionInfo.peerConnection.connectionState || 'disconnected');
          setIceState(connectionInfo.peerConnection.iceConnectionState || 'new');
          setSignalingState(connectionInfo.peerConnection.signalingState || 'stable');
        }
        
        // Update media info
        const localTracks = connectionInfo.localStream?.getTracks().length || 0;
        const remoteTracks = connectionInfo.remoteStream?.getTracks().length || 0;
        
        setMediaInfo(prev => ({
          ...prev,
          localTracks,
          remoteTracks
        }));
        
        // Update connection time
        if (startTime) {
          setConnectionStats(prev => ({
            ...prev,
            connectionTime: Date.now() - startTime.getTime()
          }));
        }
        
      } catch (error) {
        addLog('debug', 'Stats collection error', error);
      }
    }, 1000);
  }, [addLog, startTime]);

  const stopStatsCollection = useCallback(() => {
    if (statsIntervalRef.current) {
      clearInterval(statsIntervalRef.current);
      statsIntervalRef.current = null;
    }
  }, []);

  // Main test functions
  const handleStartTest = async () => {
    try {
      setIsConnecting(true);
      addLog('info', 'Starting enhanced WebRTC test...');
      
      // Start monitoring
      startUptimeCounter();
      startStatsCollection();
      
      // Connect to interview service
      const socketConnected = await interviewService.connect(config.sessionId);
      if (!socketConnected) {
        throw new Error('Failed to connect to interview service');
      }
      addLog('success', 'Connected to interview service');
      
      // Initialize WebRTC connection
      const webrtcInitialized = await webrtcService.initializeConnection(config.sessionId, true);
      if (!webrtcInitialized) {
        throw new Error('Failed to initialize WebRTC connection');
      }
      addLog('success', 'WebRTC connection initialized');
      
      // Create and send offer
      const offer = await webrtcService.createOffer();
      if (offer) {
        addLog('success', 'WebRTC offer created and sent');
      } else {
        throw new Error('Failed to create WebRTC offer');
      }
      
      setIsConnected(true);
      addLog('success', 'WebRTC test started successfully');
      
    } catch (error) {
      addLog('error', 'Failed to start WebRTC test', error);
      setIsConnected(false);
      stopUptimeCounter();
      stopStatsCollection();
    } finally {
      setIsConnecting(false);
    }
  };

  const handleStopTest = async () => {
    try {
      addLog('info', 'Stopping WebRTC test...');
      
      // Stop monitoring
      stopUptimeCounter();
      stopStatsCollection();
      
      // Cleanup WebRTC
      webrtcService.cleanup();
      addLog('info', 'WebRTC connection cleaned up');
      
      // Disconnect from interview service
      interviewService.disconnect();
      addLog('info', 'Disconnected from interview service');
      
      // Reset state
      setIsConnected(false);
      setConnectionState('disconnected');
      setIceState('new');
      setSignalingState('stable');
      
      // Reset stats
      setConnectionStats({
        bytesSent: 0,
        bytesReceived: 0,
        packetsLost: 0,
        roundTripTime: 0,
        connectionTime: 0,
        reconnectCount: 0,
        errorCount: 0
      });
      
      setMediaInfo({
        localTracks: 0,
        remoteTracks: 0,
        videoResolution: '-',
        frameRate: 0,
        videoEnabled: true,
        audioEnabled: true
      });
      
      addLog('success', 'WebRTC test stopped successfully');
      
    } catch (error) {
      addLog('error', 'Error stopping WebRTC test', error);
    }
  };

  const handleRestartTest = async () => {
    addLog('info', 'Restarting WebRTC test...');
    await handleStopTest();
    setTimeout(() => {
      handleStartTest();
    }, 1000);
  };

  // Utility functions
  const clearLogs = () => {
    setLogs([]);
    addLog('info', 'Logs cleared');
  };

  const exportLogs = () => {
    const logsText = logs.map(log => 
      `${log.timestamp.toISOString()} [${log.level.toUpperCase()}] ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
    ).join('\n');
    
    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `webrtc-test-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    addLog('info', 'Logs exported successfully');
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000) % 60;
    const minutes = Math.floor(ms / 60000) % 60;
    const hours = Math.floor(ms / 3600000);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Setup WebRTC event listeners
  useEffect(() => {
    const handleConnectionStateChange = (state: string) => {
      setConnectionState(state);
      setIsConnected(state === 'connected');
      addLog('info', `Connection state changed: ${state}`);
    };

    const handleRemoteStream = (stream: MediaStream) => {
      addLog('success', `Remote stream received: ${stream.getTracks().length} tracks`);
      setMediaInfo(prev => ({
        ...prev,
        remoteTracks: stream.getTracks().length
      }));
    };

    // Setup event listeners (these would need to be implemented in webrtcService)
    webrtcService.onConnectionState?.(handleConnectionStateChange);
    webrtcService.onRemoteStream?.(handleRemoteStream);

    return () => {
      // Cleanup
      stopUptimeCounter();
      stopStatsCollection();
      if (isConnected) {
        handleStopTest();
      }
    };
  }, [isConnected, addLog, stopUptimeCounter, stopStatsCollection]);

  return (
    <Box p={6} maxW="1200px" mx="auto">
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card>
          <CardHeader>
            <Heading size="lg" textAlign="center">
              Enhanced WebRTC Test Client
            </Heading>
            <Text textAlign="center" color="gray.600">
              Comprehensive WebRTC testing with advanced monitoring and diagnostics
            </Text>
          </CardHeader>
        </Card>

        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <HStack>
              <FiSettings />
              <Heading size="md">Configuration</Heading>
            </HStack>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
              <FormControl>
                <FormLabel>Session ID</FormLabel>
                <Input 
                  value={config.sessionId}
                  onChange={(e) => setConfig(prev => ({ ...prev, sessionId: e.target.value }))}
                  isDisabled={isConnected}
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Video Quality</FormLabel>
                <Select 
                  value={config.videoQuality}
                  onChange={(e) => setConfig(prev => ({ ...prev, videoQuality: e.target.value as any }))}
                  isDisabled={isConnected}
                >
                  <option value="low">Low (320x240)</option>
                  <option value="medium">Medium (640x480)</option>
                  <option value="high">High (1280x720)</option>
                </Select>
              </FormControl>
              
              <FormControl display="flex" alignItems="center">
                <FormLabel mb="0">Audio Enabled</FormLabel>
                <Switch 
                  isChecked={config.audioEnabled}
                  onChange={(e) => setConfig(prev => ({ ...prev, audioEnabled: e.target.checked }))}
                  isDisabled={isConnected}
                />
              </FormControl>
              
              <FormControl display="flex" alignItems="center">
                <FormLabel mb="0">Auto Reconnect</FormLabel>
                <Switch 
                  isChecked={config.autoReconnect}
                  onChange={(e) => setConfig(prev => ({ ...prev, autoReconnect: e.target.checked }))}
                />
              </FormControl>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Status and Controls */}
        <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={6}>
          <Card>
            <CardHeader>
              <HStack>
                <FiActivity />
                <Heading size="md">Connection Status</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                {/* Status Indicators */}
                <SimpleGrid columns={3} spacing={4}>
                  <Stat>
                    <StatLabel>Connection</StatLabel>
                    <StatNumber>
                      <Badge colorScheme={isConnected ? 'green' : 'red'} size="lg">
                        {connectionState}
                      </Badge>
                    </StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>ICE State</StatLabel>
                    <StatNumber>
                      <Badge colorScheme={iceState === 'connected' ? 'green' : 'yellow'} size="lg">
                        {iceState}
                      </Badge>
                    </StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Signaling</StatLabel>
                    <StatNumber>
                      <Badge colorScheme={signalingState === 'stable' ? 'green' : 'blue'} size="lg">
                        {signalingState}
                      </Badge>
                    </StatNumber>
                  </Stat>
                </SimpleGrid>

                <Divider />

                {/* Media Information */}
                <SimpleGrid columns={2} spacing={4}>
                  <Stat>
                    <StatLabel>Local Tracks</StatLabel>
                    <StatNumber>{mediaInfo.localTracks}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Remote Tracks</StatLabel>
                    <StatNumber>{mediaInfo.remoteTracks}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Video Resolution</StatLabel>
                    <StatNumber fontSize="sm">{mediaInfo.videoResolution}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Frame Rate</StatLabel>
                    <StatNumber>{mediaInfo.frameRate}fps</StatNumber>
                  </Stat>
                </SimpleGrid>

                <Divider />

                {/* Performance Stats */}
                <SimpleGrid columns={2} spacing={4}>
                  <Stat>
                    <StatLabel>Bytes Sent</StatLabel>
                    <StatNumber fontSize="sm">{formatBytes(connectionStats.bytesSent)}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Bytes Received</StatLabel>
                    <StatNumber fontSize="sm">{formatBytes(connectionStats.bytesReceived)}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Packets Lost</StatLabel>
                    <StatNumber>{connectionStats.packetsLost}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Round Trip Time</StatLabel>
                    <StatNumber>{connectionStats.roundTripTime.toFixed(1)}ms</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Connection Time</StatLabel>
                    <StatNumber fontSize="sm">{formatTime(connectionStats.connectionTime)}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Uptime</StatLabel>
                    <StatNumber fontSize="sm">{uptime}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Reconnects</StatLabel>
                    <StatNumber>{connectionStats.reconnectCount}</StatNumber>
                  </Stat>

                  <Stat>
                    <StatLabel>Errors</StatLabel>
                    <StatNumber color={connectionStats.errorCount > 0 ? 'red.500' : 'inherit'}>
                      {connectionStats.errorCount}
                    </StatNumber>
                  </Stat>
                </SimpleGrid>
              </VStack>
            </CardBody>
          </Card>

          {/* Control Panel */}
          <Card>
            <CardHeader>
              <Heading size="md">Controls</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                {/* Connection Controls */}
                <VStack spacing={2} w="full">
                  <Text fontWeight="semibold" fontSize="sm">Connection</Text>
                  <Button
                    leftIcon={<FiPlay />}
                    colorScheme="green"
                    onClick={handleStartTest}
                    isDisabled={isConnected || isConnecting}
                    isLoading={isConnecting}
                    loadingText="Starting..."
                    w="full"
                  >
                    Start Test
                  </Button>

                  <Button
                    leftIcon={<FiSquare />}
                    colorScheme="red"
                    onClick={handleStopTest}
                    isDisabled={!isConnected && !isConnecting}
                    w="full"
                  >
                    Stop Test
                  </Button>

                  <Button
                    leftIcon={<FiRefreshCw />}
                    colorScheme="orange"
                    onClick={handleRestartTest}
                    isDisabled={!isConnected}
                    w="full"
                  >
                    Restart Test
                  </Button>
                </VStack>

                <Divider />

                {/* Media Controls */}
                <VStack spacing={2} w="full">
                  <Text fontWeight="semibold" fontSize="sm">Media</Text>
                  <HStack w="full">
                    <Tooltip label="Toggle Video">
                      <IconButton
                        aria-label="Toggle Video"
                        icon={mediaInfo.videoEnabled ? <FiVideo /> : <FiVideoOff />}
                        colorScheme={mediaInfo.videoEnabled ? 'green' : 'red'}
                        isDisabled={!isConnected}
                        size="sm"
                      />
                    </Tooltip>

                    <Tooltip label="Toggle Audio">
                      <IconButton
                        aria-label="Toggle Audio"
                        icon={mediaInfo.audioEnabled ? <FiMic /> : <FiMicOff />}
                        colorScheme={mediaInfo.audioEnabled ? 'green' : 'red'}
                        isDisabled={!isConnected}
                        size="sm"
                      />
                    </Tooltip>

                    <Tooltip label="Switch Camera">
                      <IconButton
                        aria-label="Switch Camera"
                        icon={<FiCamera />}
                        colorScheme="blue"
                        isDisabled={!isConnected}
                        size="sm"
                      />
                    </Tooltip>

                    <Tooltip label="Connection Status">
                      <IconButton
                        aria-label="Connection Status"
                        icon={isConnected ? <FiWifi /> : <FiWifiOff />}
                        colorScheme={isConnected ? 'green' : 'red'}
                        isDisabled
                        size="sm"
                      />
                    </Tooltip>
                  </HStack>
                </VStack>

                <Divider />

                {/* Log Controls */}
                <VStack spacing={2} w="full">
                  <Text fontWeight="semibold" fontSize="sm">Logs</Text>
                  <HStack w="full">
                    <Button
                      leftIcon={<FiDownload />}
                      size="sm"
                      onClick={exportLogs}
                      isDisabled={logs.length === 0}
                      flex={1}
                    >
                      Export
                    </Button>

                    <Button
                      leftIcon={<FiTrash2 />}
                      size="sm"
                      colorScheme="red"
                      variant="outline"
                      onClick={clearLogs}
                      isDisabled={logs.length === 0}
                      flex={1}
                    >
                      Clear
                    </Button>
                  </HStack>
                </VStack>
              </VStack>
            </CardBody>
          </Card>
        </Grid>

        {/* Logs Panel */}
        <Card>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Debug Logs ({logs.length})</Heading>
              <Badge colorScheme="blue">{logs.length} entries</Badge>
            </HStack>
          </CardHeader>
          <CardBody>
            <Box
              h="300px"
              overflowY="auto"
              bg="gray.900"
              color="white"
              p={4}
              borderRadius="md"
              fontFamily="mono"
              fontSize="sm"
            >
              {logs.length === 0 ? (
                <Text color="gray.400" textAlign="center" mt={8}>
                  No logs yet. Start a test to see debug information.
                </Text>
              ) : (
                <VStack spacing={1} align="stretch">
                  {logs.map((log, index) => (
                    <HStack key={index} spacing={3} align="flex-start">
                      <Text color="gray.400" minW="60px" fontSize="xs">
                        {log.timestamp.toLocaleTimeString()}
                      </Text>
                      <Badge
                        colorScheme={
                          log.level === 'error' ? 'red' :
                          log.level === 'warning' ? 'yellow' :
                          log.level === 'success' ? 'green' :
                          log.level === 'debug' ? 'gray' : 'blue'
                        }
                        size="sm"
                        minW="60px"
                        textAlign="center"
                      >
                        {log.level.toUpperCase()}
                      </Badge>
                      <Text flex={1} wordBreak="break-word">
                        {log.message}
                        {log.data && (
                          <Code ml={2} fontSize="xs" colorScheme="gray">
                            {JSON.stringify(log.data)}
                          </Code>
                        )}
                      </Text>
                    </HStack>
                  ))}
                </VStack>
              )}
            </Box>
          </CardBody>
        </Card>
      </VStack>
    </Box>
  );
};
