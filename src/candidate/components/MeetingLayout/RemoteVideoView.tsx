import React, { useEffect, useRef } from 'react';
import { Box, Text } from '@chakra-ui/react';
import { motion } from 'framer-motion';

interface RemoteVideoViewProps {
  stream: MediaStream | null;
  isConnected: boolean;
}

export const RemoteVideoView: React.FC<RemoteVideoViewProps> = ({
  stream,
  isConnected,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch(console.error);
    }
  }, [stream]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '80%',
        maxWidth: '800px',
        height: '60%',
        maxHeight: '600px',
        zIndex: 10,
      }}
    >
      <Box
        w="100%"
        h="100%"
        bg="rgba(15, 23, 42, 0.9)"
        borderRadius="2xl"
        overflow="hidden"
        border="2px solid"
        borderColor={isConnected ? 'green.400' : 'gray.400'}
        display="flex"
        alignItems="center"
        justifyContent="center"
        position="relative"
      >
        {stream && isConnected ? (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
        ) : (
          <Box textAlign="center" color="whiteAlpha.700">
            <Text fontSize="xl" mb={2}>
              {isConnected ? '📹 Đang kết nối video...' : '🔌 Chưa kết nối WebRTC'}
            </Text>
            <Text fontSize="sm" opacity={0.8}>
              {isConnected ? 'Vui lòng đợi video call kết nối' : 'Đang thiết lập kết nối video'}
            </Text>
          </Box>
        )}
        
        {/* Connection Status Indicator */}
        <Box
          position="absolute"
          top="16px"
          right="16px"
          bg={isConnected ? 'green.500' : 'red.500'}
          color="white"
          px={3}
          py={1}
          borderRadius="full"
          fontSize="sm"
          fontWeight="bold"
        >
          {isConnected ? 'Connected' : 'Disconnected'}
        </Box>
      </Box>
    </motion.div>
  );
}; 