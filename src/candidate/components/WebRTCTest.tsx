import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Badge,
  Progress,
  Divider,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Textarea,
  Input,
  FormControl,
  FormLabel,
  Select,
  Switch,
  IconButton,
  Tooltip
} from '@chakra-ui/react';
import {
  FiPlay,
  FiSquare,
  FiRefreshCw,
  FiVideo,
  FiVideoOff,
  FiMic,
  FiMicOff,
  FiCamera,
  FiWifi,
  FiWifiOff,
  FiDownload,
  FiTrash2
} from 'react-icons/fi';
import { webrtcService } from '../services/webrtcService';
import { interviewService } from '../services/interviewService';

export const WebRTCTest: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('disconnected');
  const [hasLocalStream, setHasLocalStream] = useState(false);
  const [hasRemoteStream, setHasRemoteStream] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [sessionId, setSessionId] = useState('');

  const addLog = (message: string) => {
    setLogs(prev => [...prev.slice(-10), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    // Set up WebRTC event listeners
    webrtcService.onConnectionState((state) => {
      setConnectionState(state);
      setIsConnected(state === 'connected');
      addLog(`Connection state: ${state}`);
    });

    webrtcService.onRemoteStream((stream) => {
      setHasRemoteStream(true);
      addLog(`Remote stream received: ${stream.getTracks().length} tracks`);
    });

    return () => {
      webrtcService.cleanup();
    };
  }, []);

  const handleStartTest = async () => {
    try {
      addLog('Starting WebRTC test...');
      
      // Connect to socket first
      const testSessionId = `test_${Date.now()}`;
      setSessionId(testSessionId);
      
      const socketConnected = await interviewService.connect(testSessionId);
      if (!socketConnected) {
        addLog('Failed to connect to socket');
        return;
      }
      addLog('Socket connected successfully');

      // Initialize WebRTC
      const webrtcInitialized = await webrtcService.initializeConnection(testSessionId, true);
      if (!webrtcInitialized) {
        addLog('Failed to initialize WebRTC');
        return;
      }
      addLog('WebRTC initialized successfully');

      // Check local stream
      const isLocalReady = webrtcService.isLocalStreamReady();
      setHasLocalStream(isLocalReady);
      addLog(`Local stream: ${isLocalReady ? 'Available' : 'Not available'}`);
      
      // Get detailed connection info
      const connectionInfo = webrtcService.getConnectionInfo();
      addLog(`Connection info: ${JSON.stringify(connectionInfo, null, 2)}`);

      // Create offer
      const offer = await webrtcService.createOffer();
      if (offer) {
        addLog('WebRTC offer created successfully');
      } else {
        addLog('Failed to create WebRTC offer');
      }

    } catch (error) {
      addLog(`Error: ${error}`);
    }
  };

  const handleStopTest = () => {
    webrtcService.cleanup();
    interviewService.disconnect();
    setIsConnected(false);
    setConnectionState('disconnected');
    setHasLocalStream(false);
    setHasRemoteStream(false);
    setSessionId('');
    addLog('Test stopped, resources cleaned up');
  };

  return (
    <Box p={6} bg="gray.50" borderRadius="lg" maxW="600px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold" textAlign="center">
          WebRTC Connection Test
        </Text>
        
        <HStack justify="space-between">
          <VStack align="start" spacing={2}>
            <HStack>
              <Text fontSize="sm">Connection State:</Text>
              <Badge colorScheme={isConnected ? 'green' : 'red'}>
                {connectionState}
              </Badge>
            </HStack>
            
            <HStack>
              <Text fontSize="sm">Local Stream:</Text>
              <Badge colorScheme={hasLocalStream ? 'green' : 'gray'}>
                {hasLocalStream ? 'Available' : 'Not Available'}
              </Badge>
            </HStack>
            
            <HStack>
              <Text fontSize="sm">Remote Stream:</Text>
              <Badge colorScheme={hasRemoteStream ? 'green' : 'gray'}>
                {hasRemoteStream ? 'Available' : 'Not Available'}
              </Badge>
            </HStack>
            
            {sessionId && (
              <HStack>
                <Text fontSize="sm">Session ID:</Text>
                <Text fontSize="xs" color="gray.600">
                  {sessionId}
                </Text>
              </HStack>
            )}
          </VStack>
        </HStack>

        <HStack justify="center" spacing={4}>
          <Button 
            colorScheme="blue" 
            onClick={handleStartTest}
            disabled={isConnected}
          >
            Start Test
          </Button>
          
          <Button 
            colorScheme="red" 
            onClick={handleStopTest}
            disabled={!isConnected}
          >
            Stop Test
          </Button>
        </HStack>

        <Box>
          <Text fontSize="sm" fontWeight="bold" mb={2}>
            Logs:
          </Text>
          <Box
            bg="black"
            color="green.300"
            p={3}
            borderRadius="md"
            fontSize="xs"
            fontFamily="monospace"
            maxH="200px"
            overflowY="auto"
          >
            {logs.length === 0 ? (
              <Text color="gray.500">No logs yet...</Text>
            ) : (
              logs.map((log, index) => (
                <Text key={index}>{log}</Text>
              ))
            )}
          </Box>
        </Box>
      </VStack>
    </Box>
  );
}; 