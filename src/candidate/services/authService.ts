/**
 * クライアント側認証サービス
 * JWT認証、自動リフレッシュ、セキュアなトークン管理
 */
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenResponse,
  InterviewTokenVerification,
  ProfileUpdateRequest,
  PasswordChangeRequest,
  ApiResponse,
  AuthError
} from '@mensetsu-kun/shared/types/auth';

class AuthService {
  private apiBaseUrl: string;
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;
  private refreshPromise: Promise<string> | null = null;

  constructor() {
    this.apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';
  }

  /**
   * ログイン
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // クッキーを含める
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'ログインに失敗しました');
      }

      const loginResponse: LoginResponse = {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        tokenType: data.token_type,
        expiresIn: data.expires_in,
        user: {} as User // hoặc tạo User object rỗng
      };

      // アクセストークンをメモリに保存
      this.setAccessToken(data.access_token);

      // 自動リフレッシュの開始
      this.startTokenRefresh();

      return loginResponse;

    } catch (error) {
      console.error('ログインエラー:', error);
      throw error;
    }
  }

  /**
   * ログアウト
   */
  async logout(): Promise<void> {
    try {
      await fetch(`${this.apiBaseUrl}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('ログアウトエラー:', error);
    } finally {
      this.clearTokens();
      this.stopTokenRefresh();
    }
  }

  /**
   * 全デバイスからログアウト
   */
  async logoutAll(): Promise<void> {
    try {
      const token = this.getAccessToken();
      if (!token) return;

      await fetch(`${this.apiBaseUrl}/auth/logout-all`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
      });
    } catch (error) {
      console.error('全デバイスログアウトエラー:', error);
    } finally {
      this.clearTokens();
      this.stopTokenRefresh();
    }
  }

  /**
   * 現在のユーザー情報を取得
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const token = await this.getValidToken();
      if (!token) return null;

      const response = await fetch(`${this.apiBaseUrl}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          // 認証エラーの場合、トークンをクリア
          this.clearTokens();
          return null;
        }
        throw new Error('ユーザー情報の取得に失敗しました');
      }

      const data = await response.json();

      return data;

    } catch (error) {
      console.error('ユーザー情報取得エラー:', error);
      return null;
    }
  }

  /**
   * 面接トークンの検証
   */
  async verifyInterviewToken(token: string): Promise<InterviewTokenVerification> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/interview-token/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error,
          message: data.message
        };
      }

      return {
        success: true,
        interview: data.interview
      };

    } catch (error) {
      console.error('面接トークン検証エラー:', error);
      return {
        success: false,
        error: 'verification_failed',
        message: '面接トークンの検証中にエラーが発生しました'
      };
    }
  }

  /**
   * 面接開始（トークン使用）
   */
  async startInterview(token: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/interview-token/use`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Interview-Token': token,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '面接の開始に失敗しました');
      }

      return data;

    } catch (error) {
      console.error('面接開始エラー:', error);
      throw error;
    }
  }

  /**
   * プロファイルの更新
   */
  async updateProfile(profileData: ProfileUpdateRequest): Promise<User> {
    try {
      const token = await this.getValidToken();
      if (!token) throw new Error('認証が必要です');

      const response = await fetch(`${this.apiBaseUrl}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
        body: JSON.stringify(profileData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'プロファイルの更新に失敗しました');
      }

      return data.user;

    } catch (error) {
      console.error('プロファイル更新エラー:', error);
      throw error;
    }
  }

  /**
   * パスワードの変更
   */
  async changePassword(passwordData: PasswordChangeRequest): Promise<void> {
    try {
      const token = await this.getValidToken();
      if (!token) throw new Error('認証が必要です');

      const response = await fetch(`${this.apiBaseUrl}/auth/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
        body: JSON.stringify(passwordData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'パスワードの変更に失敗しました');
      }

    } catch (error) {
      console.error('パスワード変更エラー:', error);
      throw error;
    }
  }

  /**
   * 有効なトークンを取得（必要に応じて自動リフレッシュ）
   */
  async getValidToken(): Promise<string | null> {
    const token = this.getAccessToken();
    
    if (!token) {
      // トークンがない場合はリフレッシュを試行
      const refreshedToken = await this.refreshToken();
      return refreshedToken;
    }

    // トークンの有効期限をチェック（簡易版）
    if (this.isTokenExpiringSoon(token)) {
      const refreshedToken = await this.refreshToken();
      return refreshedToken || token;
    }

    return token;
  }

  /**
   * トークンの自動リフレッシュ
   */
  private async refreshToken(): Promise<string | null> {
    // すでにリフレッシュ中の場合は同じPromiseを返す
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const newToken = await this.refreshPromise;
      return newToken;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 実際のトークンリフレッシュ処理
   */
  private async performTokenRefresh(): Promise<string | null> {
    try {
      const refreshToken = this.getAccessToken(); // Lấy refresh token từ localStorage/cookie/biến lưu trữ

      const response = await fetch(`${this.apiBaseUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      if (!response.ok) {
        // リフレッシュに失敗した場合はログアウト
        this.clearTokens();
        this.stopTokenRefresh();
        return null;
      }

      const data: RefreshTokenResponse = await response.json();

      // 新しいトークンを保存
      this.setAccessToken(data.accessToken);

      return data.accessToken;

    } catch (error) {
      console.error('トークンリフレッシュエラー:', error);
      this.clearTokens();
      this.stopTokenRefresh();
      return null;
    }
  }

  /**
   * 自動リフレッシュの開始
   */
  private startTokenRefresh(): void {
    this.stopTokenRefresh();

    // 13分ごとにリフレッシュ（トークンの有効期限15分より少し早め）
    this.refreshTimer = setInterval(() => {
      this.refreshToken();
    }, 13 * 60 * 1000);
  }

  /**
   * 自動リフレッシュの停止
   */
  private stopTokenRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * アクセストークンをメモリに保存
   */
  private setAccessToken(token: string): void {
    // メモリ内変数として保存（セキュリティのためlocalStorageは使用しない）
    (window as any).__authToken = token;

    // Cookie với domain để chia sẻ giữa localhost:3000 và localhost:3001
    document.cookie = `access_token=${token}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`;
  }

  /**
   * メモリからアクセストークンを取得
   */
  private getAccessToken(): string | null {
    const cookies = document.cookie.split(";");
    const tokenCookie = cookies.find((cookie) =>
      cookie.trim().startsWith("access_token=")
    );
    const token = tokenCookie ? tokenCookie.split("=")[1] : null;
    // console.log('🔍 getAccessToken called, returning:', token);

    return token;
  }


  /**
   * すべてのトークンをクリア
   */
  private clearTokens(): void {
    delete (window as any).__authToken;
  }

  /**
   * トークンの有効期限が近いかチェック
   */
  private isTokenExpiringSoon(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp * 1000; // ミリ秒に変換
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;
      
      return exp - now < fiveMinutes;
    } catch (error) {
      // パースエラーの場合は期限切れとみなす
      return true;
    }
  }

  /**
   * 認証状態をチェック
   */
  isAuthenticated(): boolean {
    return this.getAccessToken() !== null;
  }

  /**
   * 初期化時の認証状態復元
   */
  async initializeAuth(): Promise<User | null> {
    try {
      // リフレッシュトークンでの認証復元を試行
      const token = await this.refreshToken();
      
      if (token) {
        this.startTokenRefresh();
        return await this.getCurrentUser();
      }
      
      return null;
    } catch (error) {
      console.error('認証初期化エラー:', error);
      return null;
    }
  }
}

export default new AuthService();