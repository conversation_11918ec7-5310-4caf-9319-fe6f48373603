// WebRTC Service - Simple WebRTC implementation for video calling
export interface WebRTCMessage {
  type: 'offer' | 'answer' | 'candidate' | 'ready' | 'ack' | 'error';
  data: any;
  sessionId: string;
  timestamp: string;
}

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  video: boolean;
  audio: boolean;
}

export class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private sessionId: string = '';
  private isInitiator: boolean = false;
  private iceCandidateQueue: RTCIceCandidate[] = [];
  
  // Event handlers
  private onRemoteStreamHandler: ((stream: MediaStream) => void) | null = null;
  private onConnectionStateHandler: ((state: string) => void) | null = null;
  private onSignalingMessageHandler: ((message: WebRTCMessage) => void) | null = null;

  private config: WebRTCConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ],
    video: true,
    audio: true
  };

  constructor() {
    console.log('WebRTC Service initialized');
  }

  // Initialize WebRTC connection
  async initializeConnection(sessionId: string, isInitiator: boolean = false): Promise<boolean> {
    try {
      this.sessionId = sessionId;
      this.isInitiator = isInitiator;

      // Create peer connection
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      });

      // Set up event handlers
      this.setupPeerConnectionHandlers();

      // Get local media stream
      this.localStream = await this.getLocalStream();
      
      // Add tracks to peer connection if stream exists
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          if (this.peerConnection && this.localStream) {
            this.peerConnection.addTrack(track, this.localStream);
          }
        });
      } else {
        console.warn('No local stream available, WebRTC will work in receive-only mode');
      }

      console.log('WebRTC connection initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize WebRTC connection:', error);
      return false;
    }
  }

  // Get local media stream
  private async getLocalStream(): Promise<MediaStream | null> {
    try {
      return await navigator.mediaDevices.getUserMedia({
        video: this.config.video,
        audio: this.config.audio
      });
    } catch (error: any) {
      console.error('Failed to get local media stream:', error);
      
      // Handle different error types
      if (error.name === 'NotAllowedError') {
        console.warn('User denied camera/microphone access');
      } else if (error.name === 'NotFoundError') {
        console.warn('No camera/microphone device found');
      } else if (error.name === 'NotReadableError') {
        console.warn('Camera/microphone is being used by another application');
      }
      
      // Return null instead of throwing, so WebRTC can still work for receiving
      return null;
    }
  }

  // Setup peer connection event handlers
  private setupPeerConnectionHandlers(): void {
    if (!this.peerConnection) return;

    // Handle remote stream
    this.peerConnection.ontrack = (event) => {
      const [remoteStream] = event.streams;
      this.remoteStream = remoteStream;
      if (this.onRemoteStreamHandler) {
        this.onRemoteStreamHandler(remoteStream);
      }
    };

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.onSignalingMessageHandler) {
        this.onSignalingMessageHandler({
          type: 'candidate',
          data: event.candidate,
          sessionId: this.sessionId,
          timestamp: new Date().toISOString()
        });
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection && this.onConnectionStateHandler) {
        this.onConnectionStateHandler(this.peerConnection.connectionState);
      }
    };
  }

  // Create offer (for initiator)
  async createOffer(): Promise<RTCSessionDescriptionInit | null> {
    if (!this.peerConnection) return null;

    try {
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      
      console.log('WebRTC offer created successfully:', offer);
      
      if (this.onSignalingMessageHandler) {
        this.onSignalingMessageHandler({
          type: 'offer',
          data: offer,
          sessionId: this.sessionId,
          timestamp: new Date().toISOString()
        });
      }
      
      return offer;
    } catch (error) {
      console.error('Failed to create offer:', error);
      return null;
    }
  }

  // Test method to check if WebRTC is working (local stream only)
  isLocalStreamReady(): boolean {
    return this.localStream !== null && this.localStream.getTracks().length > 0;
  }

  // Get connection info for debugging
  getConnectionInfo(): any {
    if (!this.peerConnection) return { status: 'No peer connection' };
    
    return {
      signalingState: this.peerConnection.signalingState,
      connectionState: this.peerConnection.connectionState,
      iceConnectionState: this.peerConnection.iceConnectionState,
      iceGatheringState: this.peerConnection.iceGatheringState,
      hasLocalStream: this.isLocalStreamReady(),
      hasRemoteStream: this.remoteStream !== null,
      localTracks: this.localStream?.getTracks().length || 0,
      remoteTracks: this.remoteStream?.getTracks().length || 0
    };
  }

  // Create answer (for receiver) - DEPRECATED: Use handleRemoteOffer instead
  private async createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit | null> {
    console.warn('createAnswer is deprecated, use handleRemoteOffer instead');
    return null;
  }

  // Handle incoming signaling messages
  async handleSignalingMessage(message: WebRTCMessage): Promise<void> {
    if (!this.peerConnection) {
      console.warn('No peer connection available for signaling');
      return;
    }

    try {
      const currentState = this.peerConnection.signalingState;
      console.log(`Handling ${message.type} in state: ${currentState}`);

      switch (message.type) {
        case 'offer':
          // Only handle offer if we're in stable state
          if (currentState === 'stable') {
            await this.handleRemoteOffer(message.data);
          } else {
            console.warn(`Cannot handle offer in state: ${currentState}`);
          }
          break;
        
        case 'answer':
          // Only handle answer if we're in have-local-offer state
          if (currentState === 'have-local-offer') {
            await this.peerConnection.setRemoteDescription(message.data);
            console.log('Remote answer set successfully');
          } else {
            console.warn(`Cannot handle answer in state: ${currentState}`);
          }
          break;
        
        case 'candidate':
          try {
            await this.peerConnection.addIceCandidate(message.data);
            console.log('ICE candidate added successfully');
          } catch (err) {
            console.warn('Failed to add ICE candidate:', err);
            // Queue candidates if we don't have remote description yet
            // This is important for when candidates arrive before the answer
            if (!this.peerConnection.remoteDescription) {
              console.log('Queueing ICE candidate for later');
              this.iceCandidateQueue = this.iceCandidateQueue || [];
              this.iceCandidateQueue.push(message.data);
            }
          }
          break;
        
        case 'ack':
          // Acknowledge message - just log it
          console.log('Received acknowledgment from signaling server');
          break;
        
        case 'ready':
          console.log('Remote peer is ready for connection');
          break;
          
        default:
          console.warn('Unknown signaling message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to handle signaling message:', error);
    }
  }

  // Handle remote offer
  private async handleRemoteOffer(offer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) return;

    try {
      // Check if we're in a valid state to process offer
      if (this.peerConnection.signalingState !== 'stable') {
        console.warn(`Cannot handle offer in signaling state: ${this.peerConnection.signalingState}`);
        return;
      }

      console.log('Setting remote description with offer:', offer);
      // Set remote description first
      await this.peerConnection.setRemoteDescription(offer);
      console.log('Remote offer set successfully');
      
      // Create and set local answer
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);
      console.log('Local answer created successfully:', answer);
      
      // Send answer through signaling
      if (this.onSignalingMessageHandler) {
        const message: WebRTCMessage = {
          type: 'answer',
          data: answer,
          sessionId: this.sessionId,
          timestamp: new Date().toISOString()
        };
        console.log('Sending answer through signaling:', message);
        this.onSignalingMessageHandler(message);
      }
      
      // Process any queued ICE candidates
      if (this.iceCandidateQueue.length > 0) {
        console.log(`Processing ${this.iceCandidateQueue.length} queued ICE candidates after answer`);
        for (const candidate of this.iceCandidateQueue) {
          try {
            await this.peerConnection.addIceCandidate(candidate);
            console.log('Processed queued ICE candidate successfully');
          } catch (err) {
            console.warn('Failed to process queued ICE candidate:', err);
          }
        }
        this.iceCandidateQueue = [];
      }
      
      console.log('Answer created and sent successfully');
    } catch (error) {
      console.error('Failed to handle remote offer:', error);
    }
  }

  // Event listeners
  onRemoteStream(handler: (stream: MediaStream) => void): void {
    this.onRemoteStreamHandler = handler;
  }

  onConnectionState(handler: (state: string) => void): void {
    this.onConnectionStateHandler = handler;
  }

  onSignalingMessage(handler: (message: WebRTCMessage) => void): void {
    this.onSignalingMessageHandler = handler;
  }

  // Get streams (public methods)
  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  // Get local video element for testing
  attachLocalStreamToVideo(videoElement: HTMLVideoElement): boolean {
    if (this.localStream && videoElement) {
      videoElement.srcObject = this.localStream;
      return true;
    }
    return false;
  }

  // Connection state
  getConnectionState(): string {
    return this.peerConnection?.connectionState || 'disconnected';
  }

  // Cleanup
  cleanup(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.remoteStream = null;
    this.sessionId = '';
    this.isInitiator = false;
    
    console.log('WebRTC service cleaned up');
  }
}

// Export singleton instance
export const webrtcService = new WebRTCService(); 