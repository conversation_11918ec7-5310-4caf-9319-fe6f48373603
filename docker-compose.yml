# docker-compose.yml
services:
  fastapi:
    build: ./src/fastapi
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/mensetsu_db
      - REDIS_URL=redis://redis:6379
      - CHROMA_URL=http://chroma:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
    depends_on:
      - chroma
      - db
      - redis
    volumes:
      - ./src/fastapi:/app
      - ./src/fastapi/data:/app/data
      - uploads_data:/app/uploads
      - ./candidates:/app/candidates

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: mensetsu_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    volumes:
      - ./src/fastapi/data/chroma:/data
    environment:
      - IS_PERSISTENT=TRUE
      - PERSIST_DIRECTORY=/chroma/chroma
      - ANONYMIZED_TELEMETRY=FALSE
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["*"]
      - ALLOW_RESET=TRUE

volumes:
  postgres_data:
  redis_data:
  chroma_data:
  uploads_data: