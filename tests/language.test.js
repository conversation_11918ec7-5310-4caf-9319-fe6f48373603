const test = require('node:test');
const assert = require('node:assert/strict');
require('ts-node/register');

const { convertToPositiveLanguage, containsBannedWords } = require('../src/shared/constants/language');

test('convertToPositiveLanguage replaces negative words', () => {
  const input = 'この方法はダメです';
  const expected = 'この方法は別のアプローチをです';
  assert.strictEqual(convertToPositiveLanguage(input), expected);
});

test('containsBannedWords detects banned words', () => {
  assert.strictEqual(containsBannedWords('これは悪い例です'), true);
  assert.strictEqual(containsBannedWords('これは良い例です'), false);
});
